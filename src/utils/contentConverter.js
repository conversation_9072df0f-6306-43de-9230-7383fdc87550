/**
 * Content Converter Utility
 *
 * Handles conversion between different content formats for the Tiptap editor:
 * - Markdown to HTML conversion for AI-generated content
 * - Content structure preservation for chapters and sections
 * - Safe HTML sanitization for security
 */

import { hasExistingChapterHeading } from "./contentProcessing.js";

import { prodLogger } from "./prodLogger.js";

/**
 * Convert markdown tables to structured lists for better canvas rendering
 * Tables don't render well in the current editor, so we transform them into readable list format
 * @param {string} content - Content that may contain markdown tables
 * @returns {string} - Content with tables converted to structured lists
 */
const convertTablesToLists = (content) => {
  if (!content || typeof content !== "string") {
    return content;
  }

  try {
    // Check if content contains table-like syntax
    if (!content.includes("|") || !content.match(/\|.*\|/)) {
      return content;
    }

    prodLogger.info(
      "🔄 Table conversion: Processing content with potential tables"
    );

    // Match markdown tables (| header | header |)
    const tableRegex = /^\s*\|(.+\|.+)\|\s*$/gm;
    const tables = content.match(tableRegex);

    if (!tables || tables.length === 0) {
      return content;
    }

    prodLogger.info(
      `📊 Table conversion: Found ${tables.length} potential table rows`
    );

    let processedContent = content;

    // Process each table found
    const tableBlocks = content.split(/\n\s*\n/);

    for (let i = 0; i < tableBlocks.length; i++) {
      const block = tableBlocks[i];

      // Check if this block contains a table
      if (block.includes("|") && block.match(/\|.*\|/)) {
        const lines = block.split("\n").filter((line) => line.trim());
        const tableLines = lines.filter((line) => line.includes("|"));

        if (tableLines.length >= 2) {
          // Found a table - convert it
          prodLogger.info(
            `✅ Table conversion: Converting table with ${tableLines.length} rows`
          );
          const convertedTable = convertSingleTableToList(tableLines);
          processedContent = processedContent.replace(block, convertedTable);
        }
      }
    }

    return processedContent;
  } catch (error) {
    prodLogger.warn("Error converting tables to lists:", error);
    return content;
  }
};

/**
 * Convert a single markdown table to a structured list
 * @param {string[]} tableLines - Array of table lines
 * @returns {string} - Structured list representation
 */
const convertSingleTableToList = (tableLines) => {
  try {
    if (tableLines.length < 2) return tableLines.join("\n");

    // Parse header row
    const headerLine = tableLines[0];
    const headers = headerLine
      .split("|")
      .map((cell) => cell.trim())
      .filter((cell) => cell.length > 0);

    // Skip separator line (usually line 1 with dashes)
    const dataLines = tableLines.slice(2);

    if (headers.length === 0) {
      // No clear headers, convert to simple list
      return dataLines
        .map((line) => {
          const cells = line
            .split("|")
            .map((cell) => cell.trim())
            .filter((cell) => cell.length > 0);
          return `- ${cells.join(" | ")}`;
        })
        .join("\n");
    }

    // Create structured list with headers
    let listContent = `**${headers.join(" vs ")}:**\n\n`;

    dataLines.forEach((line) => {
      const cells = line
        .split("|")
        .map((cell) => cell.trim())
        .filter((cell) => cell.length > 0);

      if (cells.length > 0) {
        // Create structured entry
        const entry = headers
          .map((header, index) => {
            const value = cells[index] || "";
            return value ? `**${header}:** ${value}` : "";
          })
          .filter((item) => item)
          .join(" | ");

        if (entry) {
          listContent += `- ${entry}\n`;
        }
      }
    });

    return listContent;
  } catch (error) {
    prodLogger.warn("Error converting single table:", error);
    return tableLines.join("\n");
  }
};

/**
 * Simple markdown to HTML converter
 * Handles basic markdown elements commonly used in AI-generated content
 * @param {string} markdown - Markdown content to convert
 * @returns {string} - HTML content safe for Tiptap
 */
export const convertMarkdownToHTML = (markdown) => {
  if (!markdown || typeof markdown !== "string") {
    return "<p></p>";
  }

  try {
    let html = markdown;

    // Convert headings (# ## ### etc.)
    html = html.replace(/^### (.*$)/gim, "<h3>$1</h3>");
    html = html.replace(/^## (.*$)/gim, "<h2>$1</h2>");
    html = html.replace(/^# (.*$)/gim, "<h1>$1</h1>");

    // Convert bold text (**text** or __text__)
    html = html.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
    html = html.replace(/__(.*?)__/g, "<strong>$1</strong>");

    // Convert italic text (*text* or _text_)
    html = html.replace(/\*(.*?)\*/g, "<em>$1</em>");
    html = html.replace(/_(.*?)_/g, "<em>$1</em>");

    // Convert images (![alt text](image-url))
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, src) => {
      // Sanitize alt text and src
      const sanitizedAlt = alt.replace(/[<>"']/g, "");
      const sanitizedSrc = src.trim();

      // Add Tiptap image classes for consistent styling
      return `<img src="${sanitizedSrc}" alt="${sanitizedAlt}" class="tiptap-image max-w-full h-auto rounded-lg shadow-sm" />`;
    });

    // Convert regular links ([text](url)) - but skip if it's an image URL
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
      const urlLower = url.toLowerCase();
      const imageExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".webp",
        ".svg",
      ];
      const isImageUrl =
        imageExtensions.some((ext) => urlLower.includes(ext)) ||
        urlLower.includes("unsplash.com") ||
        urlLower.includes("images.");

      if (isImageUrl) {
        // Convert image links to img tags
        const sanitizedText = text.replace(/[<>"']/g, "");
        const sanitizedUrl = url.trim();
        return `<img src="${sanitizedUrl}" alt="${sanitizedText}" class="tiptap-image max-w-full h-auto rounded-lg shadow-sm" />`;
      } else {
        // Regular link
        return `<a href="${url.trim()}" target="_blank" rel="noopener noreferrer">${text}</a>`;
      }
    });

    // Convert unordered lists (- item or * item)
    html = html.replace(/^\s*[-*]\s+(.*$)/gim, "<li>$1</li>");
    html = html.replace(/(<li>.*<\/li>)/s, "<ul>$1</ul>");

    // Convert ordered lists (1. item)
    html = html.replace(/^\s*\d+\.\s+(.*$)/gim, "<li>$1</li>");

    // Fix nested lists (basic approach)
    html = html.replace(/<\/ul>\s*<ul>/g, "");
    html = html.replace(/<\/ol>\s*<ol>/g, "");

    // Convert markdown tables to structured lists
    html = convertTablesToLists(html);

    // Convert line breaks to paragraphs
    const paragraphs = html.split(/\n\s*\n/);
    html = paragraphs
      .map((paragraph) => {
        paragraph = paragraph.trim();
        if (!paragraph) return "";

        // Skip if already wrapped in HTML tags
        if (paragraph.startsWith("<") && paragraph.endsWith(">")) {
          return paragraph;
        }

        // Skip if it's a list item
        if (paragraph.includes("<li>")) {
          return paragraph;
        }

        return `<p>${paragraph}</p>`;
      })
      .filter((p) => p)
      .join("");

    // Clean up any empty paragraphs
    html = html.replace(/<p>\s*<\/p>/g, "");

    // Ensure we have at least one paragraph
    if (!html.trim()) {
      html = "<p></p>";
    }

    return html;
  } catch (error) {
    return "<p>Error loading content</p>";
  }
};

/**
 * Inject image suggestion cards before chapter headings (Chapter-Based Placement)
 * Places cards immediately before Chapter 2, 3, 4, etc. (skips Chapter 1)
 * @param {string} html - HTML content to inject cards into
 * @param {Object} imageSuggestions - Image suggestions by chapter
 * @returns {string} - HTML content with injected cards at chapter boundaries
 */
export const injectChapterImageCards = (html, imageSuggestions = {}) => {
  if (
    !html ||
    !imageSuggestions ||
    Object.keys(imageSuggestions).length === 0
  ) {
    return html;
  }

  let modifiedHTML = html;

  // Get all available content unit IDs from image suggestions (skip first unit: chapter-1 or section-1)
  const availableContentUnits = Object.keys(imageSuggestions)
    .filter(
      (unitId) =>
        unitId !== "chapter-1" &&
        unitId !== "section-1" &&
        (unitId.startsWith("chapter-") || unitId.startsWith("section-"))
    )
    .sort((a, b) => {
      const numA = parseInt(a.split("-")[1]);
      const numB = parseInt(b.split("-")[1]);
      return numA - numB;
    });

  if (availableContentUnits.length === 0) {
    return modifiedHTML;
  }

  // CONTENT UNIT APPROACH: Find actual chapter/section headings in HTML
  // Look for patterns like: <h2>Chapter 2: Title</h2>, <h2>Section 2: Title</h2>, or <h1>Title</h1>
  // Enhanced regex to handle both chapter and section heading formats
  const contentHeadingRegex =
    /<(h[1-6])[^>]*>(\s*(?:(?:Chapter|Section)\s+(\d+)(?::\s*)?)?[^<]*)<\/h[1-6]>/gi;
  const headingMatches = [];
  let match;

  // Find all content unit headings with their positions
  while ((match = contentHeadingRegex.exec(modifiedHTML)) !== null) {
    const fullMatch = match[0];
    const headingTag = match[1];
    const headingText = match[2];
    const unitNumber = parseInt(match[3]);
    const position = match.index;

    // Determine if this is a chapter or section based on the heading text
    const isSection = headingText.toLowerCase().includes("section");
    const unitType = isSection ? "section" : "chapter";
    const unitId = `${unitType}-${unitNumber}`;

    headingMatches.push({
      fullMatch,
      headingTag,
      headingText,
      unitNumber,
      position,
      unitId,
      unitType,
    });
  }

  // Filter headings to only include those we have image suggestions for (and skip first unit)
  const targetHeadings = headingMatches.filter(
    (heading) =>
      heading.unitNumber > 1 && // Skip first unit (Chapter 1 or Section 1)
      availableContentUnits.includes(heading.unitId) &&
      imageSuggestions[heading.unitId]?.images?.length > 0
  );

  // Process headings in reverse order to maintain positions during injection
  for (let i = targetHeadings.length - 1; i >= 0; i--) {
    const heading = targetHeadings[i];
    const unitData = imageSuggestions[heading.unitId];

    if (unitData && unitData.images?.length > 0) {
      // Create the image suggestion card HTML
      const cardHTML = createChapterImageCardHTML(heading.unitId, unitData);

      // Insert the card immediately before the content unit heading
      modifiedHTML =
        modifiedHTML.slice(0, heading.position) +
        cardHTML +
        modifiedHTML.slice(heading.position);
    } else {
      // Skip content unit - no images available
    }
  }

  // Fallback: If no content unit headings found, use the old paragraph-based approach
  if (targetHeadings.length === 0) {
    return injectChapterImageCardsLegacy(
      modifiedHTML,
      imageSuggestions,
      availableContentUnits
    );
  }

  return modifiedHTML;
};

/**
 * Legacy paragraph-based image card injection (fallback method)
 * Used when content unit headings cannot be found in the HTML content
 * @param {string} html - HTML content to inject cards into
 * @param {Object} imageSuggestions - Image suggestions by content unit
 * @param {Array} availableContentUnits - Array of content unit IDs to process
 * @returns {string} - HTML content with injected cards at paragraph intervals
 */
const injectChapterImageCardsLegacy = (
  html,
  imageSuggestions,
  availableContentUnits
) => {
  let modifiedHTML = html;

  // Find all paragraphs for interval-based placement
  const paragraphs = modifiedHTML.match(/<p[^>]*>.*?<\/p>/gi) || [];

  // If we have multiple content units and paragraphs, inject cards at intervals
  if (
    availableContentUnits.length > 0 &&
    paragraphs.length >= availableContentUnits.length
  ) {
    const intervalSize = Math.floor(
      paragraphs.length / (availableContentUnits.length + 1)
    );

    // Process content units in reverse order to maintain positions
    for (let i = availableContentUnits.length - 1; i >= 0; i--) {
      const unitId = availableContentUnits[i];
      const unitData = imageSuggestions[unitId];

      if (unitData && unitData.images?.length > 0) {
        const targetParagraphIndex = (i + 1) * intervalSize;
        if (targetParagraphIndex < paragraphs.length) {
          const targetParagraph = paragraphs[targetParagraphIndex];
          const paragraphPosition = modifiedHTML.indexOf(targetParagraph);

          if (paragraphPosition !== -1) {
            const cardHTML = createChapterImageCardHTML(unitId, unitData);
            modifiedHTML =
              modifiedHTML.slice(0, paragraphPosition) +
              cardHTML +
              modifiedHTML.slice(paragraphPosition);
          }
        }
      }
    }
  }

  return modifiedHTML;
};

/**
 * Create proper HTML structure for image suggestion card (Tiptap Node View compatible)
 * @param {string} unitId - Content unit identifier (e.g., "chapter-2" or "section-2")
 * @param {Object} unitData - Content unit data with images and metadata
 * @returns {string} - HTML structure that matches ImageSuggestionCardExtension.parseHTML()
 */
const createChapterImageCardHTML = (unitId, unitData) => {
  const imageCount = unitData.images?.length || 0;
  const searchQuery = unitData.searchQuery || "";

  // Determine unit type and create appropriate title
  const [unitType, unitNumber] = unitId.split("-");
  const isSection = unitType === "section";
  const defaultTitle = isSection
    ? `Section ${unitNumber || "1"}`
    : `Chapter ${unitNumber || "1"}`;

  const unitTitle = unitData.chapterTitle || defaultTitle; // Keep legacy field name for compatibility
  const placementId = `${unitId}-boundary`;

  // Create HTML structure that matches our ImageSuggestionCardExtension.parseHTML() expectations
  // This MUST match the tag pattern in parseHTML(): 'div[data-type="image-suggestion-card"]'
  const cardHTML = `<div data-type="image-suggestion-card" data-chapter-id="${unitId}" data-placement-id="${placementId}" data-search-query="${searchQuery}" data-image-count="${imageCount}" data-chapter-title="${unitTitle}" data-chapter-number="${unitNumber}"></div>`;

  return cardHTML;
};

/**
 * Convert AI-generated content structure to HTML for Tiptap editor
 * Handles the complete document structure with introduction, chapters, and conclusion
 * CRITICAL: Preserves user edits by returning editorHTML when available
 * @param {Object} generatedContent - AI-generated content object
 * @param {Object} imageSuggestions - Optional image suggestions for placeholder injection
 * @param {boolean} isReadOnly - Whether the editor is in read-only mode (skips image card injection)
 * @returns {string} - Complete HTML content for editor
 */
export const convertAIContentToHTML = (
  generatedContent,
  imageSuggestions = null,
  isReadOnly = false
) => {
  if (!generatedContent) {
    return "<p></p>";
  }

  // CRITICAL FIX: If user has made edits, return their HTML directly
  // This prevents the content reset loop that was breaking editor functionality
  if (generatedContent.editorHTML) {
    return generatedContent.editorHTML;
  }

  let fullHTML = "";

  try {
    // REMOVED: Explicit title injection to prevent duplicate headings
    // AI-generated content already contains title as markdown heading
    // if (generatedContent.title) {
    //   fullHTML += `<h1>${generatedContent.title}</h1>`;
    // }

    // Add introduction
    if (generatedContent.introduction?.content) {
      fullHTML += convertMarkdownToHTML(generatedContent.introduction.content);
    }

    // Add chapters or sections (handle both document types)
    const contentUnits = generatedContent.chapters || generatedContent.sections;
    if (contentUnits && Array.isArray(contentUnits)) {
      contentUnits.forEach((unit, index) => {
        if (unit.content) {
          const unitNumber = unit.number || index + 1;
          const unitTitle = unit.title;

          // Check if content already contains appropriate heading
          const hasHeading = hasExistingChapterHeading(
            unit.content,
            unitNumber,
            unitTitle
          );

          // Add unit title if not already in content and we have a title
          if (unitTitle && !hasHeading) {
            fullHTML += `<h2>${unitTitle}</h2>`;
          }
          fullHTML += convertMarkdownToHTML(unit.content);
        }
      });
    }

    // Add conclusion
    if (generatedContent.conclusion?.content) {
      fullHTML += convertMarkdownToHTML(generatedContent.conclusion.content);
    }

    // Ensure we have content
    if (!fullHTML.trim()) {
      prodLogger.warn("No content generated, using placeholder");
      fullHTML = "<p></p>";
    }

    // Only inject image suggestion cards in edit mode (not read-only)
    if (
      !isReadOnly &&
      imageSuggestions &&
      Object.keys(imageSuggestions).length > 0
    ) {
      fullHTML = injectChapterImageCards(fullHTML, imageSuggestions);
    } else if (isReadOnly) {
      // Remove any existing image suggestion cards from the content
      fullHTML = removeImageSuggestionCards(fullHTML);
    }

    return fullHTML;
  } catch (error) {
    prodLogger.error("Error converting AI content to HTML:", error);
    return "<p>Error loading generated content. Please try regenerating.</p>";
  }
};

/**
 * Remove image suggestion cards from HTML content for read-only mode
 * @param {string} html - HTML content to clean
 * @returns {string} - Cleaned HTML content
 */
export const removeImageSuggestionCards = (html) => {
  if (!html || typeof html !== "string") {
    return html;
  }

  // Remove image suggestion card divs using regex
  const cleanedHTML = html.replace(
    /<div[^>]*data-type="image-suggestion-card"[^>]*>[\s\S]*?<\/div>/gi,
    ""
  );

  // Also remove any standalone text that might have been injected
  const finalHTML = cleanedHTML.replace(
    /Image suggestions available: \d+ images for "[^"]*" \(Interactive mode disabled\)/gi,
    ""
  );

  return finalHTML.trim();
};

/**
 * Extract plain text from HTML content for word counting and analysis
 * @param {string} html - HTML content
 * @returns {string} - Plain text content
 */
export const extractTextFromHTML = (html) => {
  if (!html) return "";

  try {
    // Remove HTML tags
    const text = html.replace(/<[^>]*>/g, "");

    // Decode HTML entities
    const textarea = document.createElement("textarea");
    textarea.innerHTML = text;

    return textarea.value.trim();
  } catch (error) {
    prodLogger.error("Error extracting text from HTML:", error);
    return "";
  }
};

/**
 * Count words in content (HTML or plain text)
 * @param {string} content - Content to count words in
 * @returns {number} - Word count
 */
export const countWords = (content) => {
  if (!content) return 0;

  const text = extractTextFromHTML(content);
  const words = text.split(/\s+/).filter((word) => word.length > 0);

  return words.length;
};

/**
 * Validate HTML content for Tiptap compatibility
 * @param {string} html - HTML content to validate
 * @returns {Object} - Validation result with isValid and issues
 */
export const validateHTMLForTiptap = (html) => {
  const issues = [];
  let isValid = true;

  try {
    if (!html || typeof html !== "string") {
      issues.push("Content is empty or not a string");
      isValid = false;
    }

    // Check for unclosed tags (basic validation)
    const openTags = (html.match(/<[^\/][^>]*>/g) || []).length;
    const closeTags = (html.match(/<\/[^>]*>/g) || []).length;

    if (openTags !== closeTags) {
      issues.push("Mismatched HTML tags detected");
      isValid = false;
    }

    // Check for dangerous content
    if (html.includes("<script>") || html.includes("javascript:")) {
      issues.push("Potentially dangerous content detected");
      isValid = false;
    }
  } catch (error) {
    issues.push(`Validation error: ${error.message}`);
    isValid = false;
  }

  return {
    isValid,
    issues,
    wordCount: isValid ? countWords(html) : 0,
  };
};

/**
 * Test function for image conversion (accessible via browser console)
 * @param {string} markdown - Test markdown content with images
 * @returns {string} - Converted HTML
 */
export const testImageConversion = (markdown) => {
  prodLogger.debug("🧪 Testing image conversion with markdown:", markdown);
  const html = convertMarkdownToHTML(markdown);
  prodLogger.debug("✅ Converted HTML:", html);
  return html;
};

// Make test function available globally for browser console testing
if (typeof window !== "undefined") {
  window.testImageConversion = testImageConversion;
}

// Export all functions for easy importing
export default {
  convertMarkdownToHTML,
  convertAIContentToHTML,
  removeImageSuggestionCards,
  extractTextFromHTML,
  countWords,
  validateHTMLForTiptap,
  testImageConversion,
};
