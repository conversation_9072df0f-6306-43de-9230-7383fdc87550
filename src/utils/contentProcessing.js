/**
 * Content Processing Utilities
 * 
 * Centralized content processing functions for HTML and Markdown parsing.
 * Extracted from contentProcessingService.js to eliminate duplication.
 */

import { createLogger } from './logger.js';

// Create logger for this module
const logger = createLogger('ContentProcessing');

// Content type constants
export const CONTENT_TYPES = {
    HTML: 'html',
    MARKDOWN: 'markdown',
    PLAIN_TEXT: 'plain_text'
};

// Heading level mappings
export const HEADING_LEVELS = {
    H1: 1,
    H2: 2,
    H3: 3,
    H4: 4,
    H5: 5,
    H6: 6
};

/**
 * Detect content type based on content analysis
 * @param {string} content - Content to analyze
 * @returns {string} Detected content type
 */
export const detectContentType = (content) => {
    if (!content || typeof content !== 'string') {
        return CONTENT_TYPES.PLAIN_TEXT;
    }

    const trimmedContent = content.trim();
    
    // Check for HTML tags
    const htmlTagPattern = /<\/?[a-z][\s\S]*>/i;
    if (htmlTagPattern.test(trimmedContent)) {
        return CONTENT_TYPES.HTML;
    }

    // Check for Markdown patterns
    const markdownPatterns = [
        /^#{1,6}\s+/m,           // Headers
        /\*\*.*?\*\*/,           // Bold
        /\*.*?\*/,               // Italic
        /`.*?`/,                 // Inline code
        /```[\s\S]*?```/,        // Code blocks
        /^\s*[-*+]\s+/m,         // Unordered lists
        /^\s*\d+\.\s+/m,         // Ordered lists
        /\[.*?\]\(.*?\)/,        // Links
        /!\[.*?\]\(.*?\)/        // Images
    ];

    const hasMarkdownPatterns = markdownPatterns.some(pattern => pattern.test(trimmedContent));
    return hasMarkdownPatterns ? CONTENT_TYPES.MARKDOWN : CONTENT_TYPES.PLAIN_TEXT;
};

/**
 * Parse HTML content into structured format
 * @param {string} htmlContent - HTML content to parse
 * @returns {Array} Parsed content structure
 */
export const parseHTMLContent = (htmlContent) => {
    if (!htmlContent || typeof htmlContent !== 'string') {
        return [];
    }

    try {
        // Create a temporary DOM element to parse HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const body = doc.body;

        const parsedContent = [];
        
        // Process each child element
        for (const element of body.children) {
            const parsed = processHTMLElement(element);
            if (parsed) {
                parsedContent.push(parsed);
            }
        }

        return parsedContent;

    } catch (error) {
        logger.error('Error parsing HTML content:', error);
        return [{
            type: 'paragraph',
            content: htmlContent,
            text: htmlContent
        }];
    }
};

/**
 * Process individual HTML element
 * @param {Element} element - HTML element to process
 * @returns {Object|null} Processed element structure
 */
export const processHTMLElement = (element) => {
    if (!element || !element.tagName) {
        return null;
    }

    const tagName = element.tagName.toLowerCase();
    const textContent = element.textContent?.trim() || '';

    switch (tagName) {
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
            return {
                type: 'heading',
                level: parseInt(tagName.charAt(1)),
                content: textContent,
                text: textContent
            };

        case 'p':
            return {
                type: 'paragraph',
                content: element.innerHTML,
                text: textContent
            };

        case 'ul':
        case 'ol':
            const listItems = Array.from(element.querySelectorAll('li')).map(li => ({
                content: li.innerHTML,
                text: li.textContent?.trim() || ''
            }));
            
            return {
                type: 'list',
                listType: tagName === 'ul' ? 'unordered' : 'ordered',
                items: listItems
            };

        case 'img':
            return {
                type: 'image',
                src: element.getAttribute('src'),
                alt: element.getAttribute('alt') || '',
                title: element.getAttribute('title') || ''
            };

        case 'blockquote':
            return {
                type: 'blockquote',
                content: element.innerHTML,
                text: textContent
            };

        case 'pre':
        case 'code':
            return {
                type: 'code',
                content: textContent,
                text: textContent,
                language: element.getAttribute('class')?.replace('language-', '') || ''
            };

        case 'div':
        case 'section':
        case 'article':
            // Process container elements recursively
            const children = [];
            for (const child of element.children) {
                const processed = processHTMLElement(child);
                if (processed) {
                    children.push(processed);
                }
            }
            
            if (children.length > 0) {
                return {
                    type: 'container',
                    tagName,
                    children
                };
            }
            
            // If no children, treat as paragraph
            if (textContent) {
                return {
                    type: 'paragraph',
                    content: element.innerHTML,
                    text: textContent
                };
            }
            break;

        default:
            // For other elements, extract text content
            if (textContent) {
                return {
                    type: 'paragraph',
                    content: element.innerHTML,
                    text: textContent
                };
            }
    }

    return null;
};

/**
 * Convert Markdown to HTML
 * @param {string} markdownContent - Markdown content to convert
 * @returns {Promise<string>} Converted HTML content
 */
export const convertMarkdownToHTML = async (markdownContent) => {
    if (!markdownContent || typeof markdownContent !== 'string') {
        return '';
    }

    try {
        // Dynamic import to avoid bundling issues
        const { unified } = await import('unified');
        const { default: remarkParse } = await import('remark-parse');
        const { default: remarkRehype } = await import('remark-rehype');
        const { default: rehypeStringify } = await import('rehype-stringify');

        const processor = unified()
            .use(remarkParse)
            .use(remarkRehype)
            .use(rehypeStringify);

        const result = await processor.process(markdownContent);
        return String(result);

    } catch (error) {
        logger.error('Error converting Markdown to HTML:', error);
        
        // Fallback: basic Markdown to HTML conversion
        return convertMarkdownBasic(markdownContent);
    }
};

/**
 * Basic Markdown to HTML conversion (fallback)
 * @param {string} markdown - Markdown content
 * @returns {string} Basic HTML conversion
 */
const convertMarkdownBasic = (markdown) => {
    let html = markdown;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

    // Bold
    html = html.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');

    // Italic
    html = html.replace(/\*(.*)\*/gim, '<em>$1</em>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>');

    // Images
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/gim, '<img alt="$1" src="$2" />');

    // Line breaks
    html = html.replace(/\n\n/gim, '</p><p>');
    html = '<p>' + html + '</p>';

    return html;
};

/**
 * Detect if content already contains a chapter heading
 * @param {string} content - Content to check (HTML or markdown)
 * @param {number} expectedChapterNumber - Expected chapter number to look for
 * @param {string} expectedChapterTitle - Expected chapter title to look for (optional)
 * @returns {boolean} True if chapter heading is found
 */
export const hasExistingChapterHeading = (content, expectedChapterNumber, expectedChapterTitle = null) => {
    if (!content || typeof content !== 'string') {
        return false;
    }

    // Normalize content for checking
    const normalizedContent = content.trim();

    // Check for HTML chapter headings (h1-h6 tags) - both old and new formats
    const htmlChapterRegex = new RegExp(
        `<h[1-6][^>]*>\\s*Chapter\\s+${expectedChapterNumber}(?::|\\s|<|\$)`,
        'i'
    );

    // Check for Markdown chapter headings (# ## ### etc.) - both old and new formats
    const markdownChapterRegex = new RegExp(
        `^#{1,6}\\s+Chapter\\s+${expectedChapterNumber}(?::|\\s|\$)`,
        'im'
    );

    // Check for plain text chapter headings - both old and new formats
    const plainTextChapterRegex = new RegExp(
        `^Chapter\\s+${expectedChapterNumber}(?::|\\s|\$)`,
        'im'
    );

    // Test old "Chapter X:" patterns first
    if (htmlChapterRegex.test(normalizedContent) ||
        markdownChapterRegex.test(normalizedContent) ||
        plainTextChapterRegex.test(normalizedContent)) {
        return true;
    }

    // If a specific title is provided, also check for title-based patterns (new format)
    if (expectedChapterTitle) {
        // Check for HTML headings with just the title (new format)
        const htmlTitleRegex = new RegExp(
            `<h[1-6][^>]*>\\s*${escapeRegex(expectedChapterTitle)}\\s*</h[1-6]>`,
            'i'
        );

        // Check for Markdown headings with just the title (new format)
        const markdownTitleRegex = new RegExp(
            `^#{1,6}\\s+${escapeRegex(expectedChapterTitle)}\\s*$`,
            'im'
        );

        // Check for plain text title (new format)
        const plainTitleRegex = new RegExp(
            `^${escapeRegex(expectedChapterTitle)}\\s*$`,
            'im'
        );

        // Also check for legacy title-based patterns (backward compatibility)
        const legacyTitleRegex = new RegExp(
            `(?:Chapter\\s+${expectedChapterNumber}[:\\s]*)?${escapeRegex(expectedChapterTitle)}`,
            'i'
        );

        if (htmlTitleRegex.test(normalizedContent) ||
            markdownTitleRegex.test(normalizedContent) ||
            plainTitleRegex.test(normalizedContent) ||
            legacyTitleRegex.test(normalizedContent)) {
            return true;
        }
    }

    return false;
};

/**
 * Escape special regex characters in a string
 * @param {string} string - String to escape
 * @returns {string} Escaped string
 */
const escapeRegex = (string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

/**
 * Parse Markdown content into structured format
 * @param {string} markdownContent - Markdown content to parse
 * @returns {Promise<Array>} Parsed content structure
 */
export const parseMarkdownContent = async (markdownContent) => {
    try {
        // Convert to HTML first, then parse
        const htmlContent = await convertMarkdownToHTML(markdownContent);
        return parseHTMLContent(htmlContent);

    } catch (error) {
        logger.error('Error parsing Markdown content:', error);
        return [{
            type: 'paragraph',
            content: markdownContent,
            text: markdownContent
        }];
    }
};

/**
 * Extract images from HTML content
 * @param {string} htmlContent - HTML content to process
 * @returns {Array} Array of image objects
 */
export const extractImagesFromHTML = (htmlContent) => {
    if (!htmlContent || typeof htmlContent !== 'string') {
        return [];
    }

    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const images = doc.querySelectorAll('img');

        return Array.from(images).map((img, index) => ({
            src: img.getAttribute('src'),
            alt: img.getAttribute('alt') || '',
            title: img.getAttribute('title') || '',
            index
        })).filter(img => img.src);

    } catch (error) {
        logger.error('Error extracting images from HTML:', error);
        return [];
    }
};

/**
 * Extract images from Markdown content
 * @param {string} markdownContent - Markdown content to process
 * @returns {Array} Array of image objects
 */
export const extractImagesFromMarkdown = (markdownContent) => {
    if (!markdownContent || typeof markdownContent !== 'string') {
        return [];
    }

    const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
    const images = [];
    let match;
    let index = 0;

    while ((match = imagePattern.exec(markdownContent)) !== null) {
        images.push({
            src: match[2],
            alt: match[1] || '',
            title: '',
            index: index++
        });
    }

    return images;
};

// Export all content processing functions
export default {
    detectContentType,
    parseHTMLContent,
    parseMarkdownContent,
    convertMarkdownToHTML,
    processHTMLElement,
    extractImagesFromHTML,
    extractImagesFromMarkdown,
    CONTENT_TYPES,
    HEADING_LEVELS
};
