/**
 * Document Type-Specific AI Prompt Factory
 * Generates specialized AI prompts based on document type and configuration
 * Integrates with existing documentTypeConfigs to create type-appropriate content
 */

import { documentTypeConfigs } from "../pages/document-creator/utils/questionnaireDataStructure.js";
import { getAcademicLevelContext } from "../pages/document-creator/constants/documentOptions.js";

import { prodLogger } from "../utils/prodLogger.js";
/**
 * Main factory function to create document type-specific prompts
 * @param {Object} documentData - Complete document configuration from questionnaire
 * @param {string} promptType - Type of prompt to generate ('outline', 'title', 'content', 'introduction', 'chapter')
 * @param {Object} chapterData - Additional data for chapter-specific prompts (optional)
 * @returns {string} Specialized AI prompt for the document type
 */
export const createDocumentTypePrompt = (
  documentData,
  promptType = "outline",
  chapterData = null
) => {
  const documentType = documentData.documentPurpose?.primaryType || "ebook";
  const subType = documentData.documentPurpose?.subType;
  const config = documentTypeConfigs[documentType];

  if (!config) {
    prodLogger.warn(
      `No configuration found for document type: ${documentType}, using generic prompt`
    );
    return createGenericPrompt(documentData, promptType);
  }

  // Merge configuration with user data to get complete requirements
  const requirements = mergeDocumentRequirements(documentData, config);

  let generatedPrompt;

  // Route to appropriate prompt creator based on document type
  switch (documentType) {
    case "academic":
      generatedPrompt = createAcademicPrompt(
        documentData,
        requirements,
        subType,
        promptType,
        chapterData
      );
      break;
    case "business":
      generatedPrompt = createBusinessPrompt(
        documentData,
        requirements,
        subType,
        promptType,
        chapterData
      );
      break;
    case "ebook":
      generatedPrompt = createEbookPrompt(
        documentData,
        requirements,
        subType,
        promptType,
        chapterData
      );
      break;
    default:
      generatedPrompt = createGenericPrompt(
        documentData,
        promptType,
        chapterData
      );
  }

  // Add universal formatting guidelines to all prompts
  generatedPrompt += `

IMPORTANT FORMATTING GUIDELINES:
- Never use markdown table syntax with pipe characters (|) as it doesn't render properly in our editor
- Instead of tables, use structured bullet points or numbered lists to present data
- Format comparisons and data as clear, organized lists with descriptive labels
- Use headings and subheadings to organize complex information hierarchically
- Present statistical data in descriptive paragraphs or well-formatted lists

UNIVERSAL LANGUAGE STANDARDS (CRITICAL):
For Business & Academic Documents:
- NEVER use informal phrases like "imagine you're", "picture this", "let's dive into", "welcome"
- AVOID explanatory phrases like "this chapter explains", "this section covers", "let's explore"
- DO NOT use conversational language or casual second-person address ("you", "your")
- ELIMINATE contractions, colloquialisms, and casual expressions
- MAINTAIN professional, formal tone appropriate to document type
- PRESENT information directly without unnecessary narrative fluff or introductory phrases`;

  // Log the generated prompt for debugging
  console.log(
    `🎨 [PROMPT FACTORY] Generated ${promptType} prompt for ${documentType}${
      subType ? ` (${subType})` : ""
    }:`,
    {
      documentType: documentType,
      subType: subType,
      promptType: promptType,
      topic: documentData.topicAndNiche?.mainTopic,
      audience: documentData.audienceAnalysis?.primaryAudience,
      chapterTitle: chapterData?.title,
      promptLength: generatedPrompt.length,
      prompt: generatedPrompt,
      timestamp: new Date().toISOString(),
    }
  );

  return generatedPrompt;
};

/**
 * Merge document type configuration with user-provided data
 * @param {Object} documentData - User questionnaire data
 * @param {Object} config - Document type configuration
 * @returns {Object} Merged requirements object
 */
const mergeDocumentRequirements = (documentData, config) => {
  return {
    // Start with document type defaults
    ...config.defaultRequirements,

    // Override with user-specific choices
    citationStyle:
      documentData.additionalRequirements?.citationStyle ||
      config.defaultRequirements?.citationStyle ||
      "none",
    researchRequired:
      documentData.contentDetails?.researchRequired ??
      config.defaultRequirements?.researchRequired ??
      false,
    formalityLevel:
      documentData.toneAndVoice?.formalityLevel ||
      config.defaultRequirements?.formalityLevel ||
      "formal",
    writingStyle:
      documentData.toneAndVoice?.writingStyle ||
      config.defaultRequirements?.writingStyle ||
      "professional",
    includeExecutiveSummary:
      config.defaultRequirements?.includeExecutiveSummary ?? false,
    visualElements: config.defaultRequirements?.visualElements || {},
    organizationStyle:
      config.defaultRequirements?.organizationStyle || "logical",
    voicePreference:
      config.defaultRequirements?.voicePreference || "third-person",
  };
};

/**
 * Extract common context data from document configuration
 * @param {Object} documentData - Document configuration
 * @returns {Object} Base context for prompt generation
 */
const extractBaseContext = (documentData) => {
  return {
    topic: documentData.topicAndNiche?.mainTopic || "General Topic",
    audience:
      documentData.audienceAnalysis?.primaryAudience || "General Audience",
    title:
      documentData.titleSelection?.selectedTitle ||
      `${documentData.topicAndNiche?.mainTopic || "Document"}`,
    tone: documentData.toneAndVoice?.toneOfVoice || "informative",
    academicLevel:
      documentData.audienceAnalysis?.academicLevel ||
      documentData.audienceAnalysis?.primaryAudience,
    subNiches: documentData.topicAndNiche?.subNiches || [],
    language: documentData.topicAndNiche?.language || "english",
  };
};

/**
 * Create academic document prompts (research papers, theses, essays)
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Merged requirements
 * @param {string} subType - Academic sub-type (research-paper, thesis, essay, etc.)
 * @param {string} promptType - Type of prompt to generate
 * @returns {string} Academic-specific AI prompt
 */
const createAcademicPrompt = (
  documentData,
  requirements,
  subType,
  promptType,
  chapterData
) => {
  const baseContext = extractBaseContext(documentData);
  const academicSections = getAcademicSections(subType);

  // Get academic level context for AI prompts
  const academicLevelContext = getAcademicLevelContext(
    baseContext.academicLevel
  );
  const isAcademicLevel = ["undergraduate", "masters", "phd"].includes(
    baseContext.academicLevel
  );
  const academicLevelText = isAcademicLevel
    ? baseContext.academicLevel.charAt(0).toUpperCase() +
      baseContext.academicLevel.slice(1) +
      " level"
    : "Graduate/Research level";

  if (promptType === "sub-topics") {
    return `You are an expert academic researcher and field specialist. Generate 8 highly relevant research focus areas for the academic topic "${
      baseContext.topic
    }".

ACADEMIC CONTEXT:
- Document Title: ${baseContext.title}
- Document Type: Academic ${subType || "Paper"}
- Research Field: Determine appropriate field based on topic
- Target Audience: ${baseContext.audience}
- Academic Level: ${academicLevelText}
- Writing Context: ${academicLevelContext}

RESEARCH FOCUS REQUIREMENTS:
- Each focus area should represent a distinct research angle or methodology
- Focus on areas that would make excellent academic investigation topics
- Consider different theoretical frameworks and approaches
- Ensure they are academically rigorous and publishable
- Make them specific enough for focused research
- Ensure they are distinct from each other
- Tailor complexity and depth to ${academicLevelText} expectations

Return ONLY a valid JSON array with objects containing:
- id: kebab-case identifier (e.g., "machine-learning-healthcare")
- name: Clear, academic name (e.g., "Machine Learning Applications in Healthcare")
- description: Brief explanation of the research focus (max 80 characters)

Topic: ${baseContext.topic}
Language: ${baseContext.language || "english"}

JSON Response:`;
  }

  if (promptType === "outline") {
    return `You are an expert academic researcher and scholarly writer specializing in ${
      baseContext.topic
    }. Create a comprehensive ${
      subType || "academic paper"
    } outline for peer review and academic publication.

DOCUMENT SPECIFICATIONS:
- Type: Academic ${subType || "Paper"}
- Title: ${baseContext.title}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Academic Level: ${academicLevelText}
- Writing Context: ${academicLevelContext}

ACADEMIC REQUIREMENTS:
- Citation Style: ${requirements.citationStyle?.toUpperCase() || "APA"}
- Research Level: ${
      requirements.researchRequired
        ? "Extensive peer-reviewed sources required"
        : "Moderate research with credible sources"
    }
- Formality Level: ${requirements.formalityLevel || "Very formal"}
- Writing Style: ${requirements.writingStyle || "Academic"}
- Voice: ${requirements.voicePreference || "Third-person academic"}

REQUIRED ACADEMIC SECTIONS:
${academicSections
  .map(
    (section) =>
      `- ${section.name}: ${section.description} (${section.wordCount})`
  )
  .join("\n")}

ACADEMIC STANDARDS:
- Follow ${
      requirements.citationStyle?.toUpperCase() || "APA"
    } citation format throughout
- Include comprehensive literature review with peer-reviewed sources
- Maintain formal academic tone (avoid contractions, colloquialisms)
- Use third-person perspective and passive voice where appropriate
- Include methodology section with clear research design
- Provide statistical analysis and data interpretation where applicable
- Include limitations and future research directions
- Structure for peer review and academic publication standards
- Adapt complexity and depth to ${academicLevelText} expectations

STRICT ACADEMIC LANGUAGE STANDARDS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's explore"
- AVOID explanatory phrases like "this chapter explains", "this section covers"
- DO NOT use conversational language or second-person ("you", "your")
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, scholarly language with precise academic terminology
- MAINTAIN objective, third-person perspective throughout
- PRESENT arguments with evidence-based support without informal narrative

CONTENT REQUIREMENTS:
- Each section must be substantial and well-researched
- Include proper academic transitions between sections
- Maintain scholarly objectivity throughout
- Include relevant theoretical frameworks
- Provide evidence-based conclusions and recommendations
- Ensure logical flow from introduction to conclusion
- Tailor academic language and concepts to ${academicLevelText} readership

Return ONLY a valid JSON object with this structure:
{
  "title": "Academic paper title",
  "abstract": "Brief abstract summarizing research (150-250 words)",
  "chapters": [
    {
      "number": 1,
      "title": "Section title",
      "sections": ["Subsection 1", "Subsection 2", "Subsection 3"],
      "wordCount": "estimated word count",
      "requirements": ["specific academic requirements for this section"]
    }
  ],
  "requiredElements": ["bibliography", "appendices if needed"],
  "citationStyle": "${requirements.citationStyle || "APA"}",
  "academicLevel": "${baseContext.academicLevel || "graduate"}"
}

JSON Response:`;
  }

  if (promptType === "chapter" && chapterData) {
    return `You are an expert academic researcher and scholarly writer. Generate comprehensive content for this academic section:

Chapter ${chapterData.number}: ${chapterData.title}

ACADEMIC CONTEXT:
- Document Title: ${baseContext.title}
- Document Type: Academic ${subType || "Paper"}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Citation Style: ${requirements.citationStyle?.toUpperCase() || "APA"}
- Academic Level: ${academicLevelText}
- Writing Context: ${academicLevelContext}

CHAPTER SECTIONS TO COVER:
${
  chapterData.sections?.map((section) => `- ${section}`).join("\n") ||
  "- Main content for this chapter"
}

ACADEMIC WRITING REQUIREMENTS:
- Use formal academic language and third-person perspective
- Include relevant citations and references where appropriate
- Maintain scholarly objectivity and evidence-based arguments
- Follow ${requirements.citationStyle?.toUpperCase() || "APA"} citation format
- Include theoretical framework and literature connections
- Provide critical analysis and interpretation
- Use appropriate academic terminology for the field
- Adapt language complexity and theoretical depth to ${academicLevelText} readership

STRICT ACADEMIC LANGUAGE STANDARDS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's explore"
- AVOID explanatory phrases like "this chapter explains", "this section covers"
- DO NOT use conversational language or second-person ("you", "your")
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, scholarly language with precise academic terminology
- MAINTAIN objective, third-person perspective throughout
- PRESENT arguments with evidence-based support without informal narrative

CONTENT FORMATTING REQUIREMENTS:
- Present data and statistics using bullet points or numbered lists instead of tables
- Use structured lists for comparisons and statistical information
- Convert complex data into well-organized descriptive paragraphs
- Avoid markdown table syntax (pipes |) - use clear list formatting instead

CONTENT STRUCTURE:
- Begin with clear section introduction
- Develop arguments with supporting evidence
- Include relevant research findings and data
- Provide critical analysis and interpretation
- Conclude with implications and connections to broader research
- Use proper academic transitions between subsections
- Ensure content depth matches ${academicLevelText} expectations

LENGTH: 800-1200 words
TONE: Formal academic, objective, evidence-based

Generate the complete academic chapter content in markdown format:`;
  }

  // Handle other prompt types for academic documents
  return createAcademicContentPrompt(
    documentData,
    requirements,
    subType,
    promptType,
    chapterData
  );
};

/**
 * Get academic section structure based on sub-type
 * @param {string} subType - Academic document sub-type
 * @returns {Array} Array of section objects with requirements
 */
const getAcademicSections = (subType) => {
  const sections = {
    "research-paper": [
      {
        name: "Abstract",
        description: "Concise summary of research methodology and findings",
        wordCount: "150-250 words",
      },
      {
        name: "Introduction",
        description: "Problem statement, research questions, and significance",
        wordCount: "500-800 words",
      },
      {
        name: "Literature Review",
        description:
          "Comprehensive review of existing research and theoretical framework",
        wordCount: "1000-2000 words",
      },
      {
        name: "Methodology",
        description: "Research design, data collection, and analysis methods",
        wordCount: "800-1200 words",
      },
      {
        name: "Results",
        description:
          "Findings and data analysis with statistical interpretation",
        wordCount: "1000-1500 words",
      },
      {
        name: "Discussion",
        description: "Interpretation of results, implications, and limitations",
        wordCount: "1200-1800 words",
      },
      {
        name: "Conclusion",
        description: "Summary of contributions and future research directions",
        wordCount: "400-600 words",
      },
      {
        name: "References",
        description: "Properly formatted citations in required style",
        wordCount: "varies",
      },
    ],
    thesis: [
      {
        name: "Abstract",
        description: "Extended summary of thesis contributions and methodology",
        wordCount: "300-500 words",
      },
      {
        name: "Introduction",
        description: "Comprehensive problem introduction and research scope",
        wordCount: "2000-3000 words",
      },
      {
        name: "Literature Review",
        description: "Extensive review of field with critical analysis",
        wordCount: "5000-8000 words",
      },
      {
        name: "Theoretical Framework",
        description: "Theoretical foundation and conceptual model",
        wordCount: "3000-5000 words",
      },
      {
        name: "Methodology",
        description: "Detailed research methodology and justification",
        wordCount: "3000-4000 words",
      },
      {
        name: "Analysis",
        description: "Comprehensive data analysis and interpretation",
        wordCount: "8000-12000 words",
      },
      {
        name: "Findings",
        description: "Research findings and their significance",
        wordCount: "4000-6000 words",
      },
      {
        name: "Conclusion",
        description: "Conclusions, contributions, and future work",
        wordCount: "2000-3000 words",
      },
      {
        name: "Bibliography",
        description: "Comprehensive reference list",
        wordCount: "varies",
      },
    ],
    essay: [
      {
        name: "Introduction",
        description: "Thesis statement and essay overview",
        wordCount: "200-300 words",
      },
      {
        name: "Body Paragraphs",
        description: "Arguments supported by evidence and analysis",
        wordCount: "1500-2500 words",
      },
      {
        name: "Counter-arguments",
        description: "Addressing opposing viewpoints",
        wordCount: "300-500 words",
      },
      {
        name: "Conclusion",
        description: "Summary and final reflections",
        wordCount: "200-300 words",
      },
      {
        name: "Works Cited",
        description: "Reference list in required format",
        wordCount: "varies",
      },
    ],
    "case-study": [
      {
        name: "Executive Summary",
        description: "Brief overview of case and key findings",
        wordCount: "300-500 words",
      },
      {
        name: "Background",
        description: "Context and background information",
        wordCount: "800-1200 words",
      },
      {
        name: "Problem Statement",
        description: "Clear definition of the problem or challenge",
        wordCount: "400-600 words",
      },
      {
        name: "Analysis",
        description: "Detailed analysis using relevant frameworks",
        wordCount: "2000-3000 words",
      },
      {
        name: "Solutions",
        description: "Proposed solutions and recommendations",
        wordCount: "1000-1500 words",
      },
      {
        name: "Implementation",
        description: "Implementation plan and considerations",
        wordCount: "800-1200 words",
      },
      {
        name: "Conclusion",
        description: "Lessons learned and implications",
        wordCount: "400-600 words",
      },
      {
        name: "References",
        description: "Academic sources and citations",
        wordCount: "varies",
      },
    ],
  };

  return sections[subType] || sections["research-paper"];
};

/**
 * Get subtype-specific context for academic documents
 * @param {string} subType - Academic subtype
 * @returns {Object} Subtype-specific context
 */
const getAcademicSubtypeContext = (subType) => {
  const contexts = {
    essay: {
      titleRequirements: `ESSAY-SPECIFIC TITLE REQUIREMENTS:
- Use analytical language: "An Analysis of...", "The Impact of...", "Examining..."
- Focus on argument or thesis being presented
- Include evaluative terms: "Assessment", "Evaluation", "Critical Analysis"
- Avoid research methodology terms`,
      titleLength: "8-12 words optimal for essay format",
      titleStyles: `- Analytical: "An Analysis of [Topic] in [Context]"
- Argumentative: "The Case for/Against [Position] in [Field]"
- Comparative: "Comparing [A] and [B]: Implications for [Field]"
- Critical: "A Critical Examination of [Topic]"`,
    },
    "research-paper": {
      titleRequirements: `RESEARCH PAPER-SPECIFIC TITLE REQUIREMENTS:
- Include methodology hints: "A Study of...", "An Investigation into..."
- Mention research scope: "Effects of...", "Relationship between..."
- Use empirical language: "Evidence", "Findings", "Results"
- Include population or sample if relevant`,
      titleLength: "10-15 words optimal for research papers",
      titleStyles: `- Empirical: "The Effects of [Variable] on [Outcome]: A Study of [Population]"
- Investigative: "Investigating the Relationship between [A] and [B]"
- Experimental: "An Experimental Study of [Topic] in [Context]"
- Correlational: "The Correlation between [Variable A] and [Variable B]"`,
    },
    thesis: {
      titleRequirements: `THESIS-SPECIFIC TITLE REQUIREMENTS:
- Reflect comprehensive research scope
- Include theoretical framework hints
- Use formal academic language
- Indicate original contribution to field
- May include subtitle for clarity`,
      titleLength: "12-18 words acceptable for thesis format",
      titleStyles: `- Comprehensive: "[Topic]: A Comprehensive Analysis of [Scope]"
- Theoretical: "Toward a Theory of [Topic]: [Specific Focus]"
- Developmental: "The Development of [Concept] in [Context]"
- Contributory: "Advancing [Field]: New Perspectives on [Topic]"`,
    },
    dissertation: {
      titleRequirements: `DISSERTATION-SPECIFIC TITLE REQUIREMENTS:
- Reflect doctoral-level research depth
- Include theoretical and methodological scope
- Use sophisticated academic language
- Indicate significant original contribution
- Often includes subtitle for precision`,
      titleLength: "15-20 words acceptable for dissertation format",
      titleStyles: `- Foundational: "Foundations of [Theory]: A Comprehensive Investigation"
- Paradigmatic: "Rethinking [Field]: New Paradigms in [Specific Area]"
- Methodological: "A Mixed-Methods Investigation of [Topic]"
- Theoretical: "Toward a Unified Theory of [Concept]: Evidence from [Context]"`,
    },
    assignment: {
      titleRequirements: `ASSIGNMENT-SPECIFIC TITLE REQUIREMENTS:
- Clear and direct language appropriate for course level
- Include assignment focus or question being addressed
- Use accessible academic language
- Reflect specific course requirements
- Concise and focused scope`,
      titleLength: "6-10 words optimal for assignment format",
      titleStyles: `- Direct: "[Topic] in [Context]: Key Issues"
- Question-based: "How Does [Factor] Affect [Outcome]?"
- Problem-focused: "Addressing [Problem] in [Field]"
- Application-based: "Applying [Theory] to [Real-world Context]"`,
    },
  };

  return contexts[subType] || contexts["research-paper"];
};

/**
 * Create academic content prompts for specific sections
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Academic requirements
 * @param {string} subType - Academic sub-type
 * @param {string} promptType - Specific prompt type
 * @returns {string} Academic content prompt
 */
const createAcademicContentPrompt = (
  documentData,
  requirements,
  subType,
  promptType,
  chapterData
) => {
  const baseContext = extractBaseContext(documentData);

  // Get academic level context for AI prompts
  const academicLevelContext = getAcademicLevelContext(
    baseContext.academicLevel
  );
  const isAcademicLevel = ["undergraduate", "masters", "phd"].includes(
    baseContext.academicLevel
  );
  const academicLevelText = isAcademicLevel
    ? baseContext.academicLevel.charAt(0).toUpperCase() +
      baseContext.academicLevel.slice(1) +
      " level"
    : "Graduate/Research level";

  if (promptType === "title") {
    // Subtype-specific title generation
    const subtypeContext = getAcademicSubtypeContext(subType);

    return `You are an expert academic researcher. Generate 8 scholarly titles for a ${
      subType || "research paper"
    }:

Topic: ${baseContext.topic}
Academic Field: Determine appropriate field based on topic
Target Audience: ${baseContext.audience}
Academic Level: ${academicLevelText}
Writing Context: ${academicLevelContext}
Citation Style: ${requirements.citationStyle}
Document Subtype: ${subType || "research paper"}${
      baseContext.subNiches && baseContext.subNiches.length > 0
        ? `
Research Focus Areas: ${baseContext.subNiches
            .map((s) => s.name || s)
            .join(", ")}`
        : ""
    }

${subtypeContext.titleRequirements}

ACADEMIC TITLE REQUIREMENTS:
- Follow academic title conventions (clear, specific, informative)
- Include key variables or concepts being studied
- Avoid sensational or clickbait language
- Use appropriate academic terminology
- Length: ${subtypeContext.titleLength}
- Include subtitle if it adds clarity
- Tailor complexity to ${academicLevelText} expectations

TITLE STYLES TO INCLUDE:
${subtypeContext.titleStyles}
- Question-based: Research question format
- Declarative: Statement of findings or position
- Comparative: Comparing two or more elements

Return ONLY a valid JSON array with objects containing:
- id: unique identifier
- text: the academic title
- style: one of "descriptive", "question-based", "declarative", "comparative"
- field: suggested academic field/discipline

JSON Response:`;
  }

  if (promptType === "introduction") {
    return `You are an expert academic researcher and scholarly writer. Write a compelling introduction for your ${
      subType || "academic paper"
    }:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Academic Field: Determine appropriate field based on topic
Target Audience: ${baseContext.audience}
Academic Level: ${academicLevelText}
Writing Context: ${academicLevelContext}
Citation Style: ${requirements.citationStyle?.toUpperCase() || "APA"}

ACADEMIC INTRODUCTION REQUIREMENTS:
- Hook readers with compelling research question or problem statement
- Establish the significance and relevance of your research
- Provide necessary background context and literature foundation
- Clearly state your thesis, research questions, or hypotheses
- Outline the structure and scope of your paper
- Use formal academic language and third-person perspective
- Include relevant citations to establish credibility
- Adapt theoretical depth and language complexity to ${academicLevelText} readership

STRICT ACADEMIC LANGUAGE STANDARDS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's explore"
- AVOID explanatory phrases like "this chapter explains", "this section covers"
- DO NOT use conversational language or second-person ("you", "your")
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, scholarly language with precise academic terminology
- MAINTAIN objective, third-person perspective throughout
- PRESENT arguments with evidence-based support without informal narrative

INTRODUCTION STRUCTURE:
- Opening hook with research significance
- Background context and problem identification
- Literature gap or research opportunity
- Clear thesis statement or research questions
- Methodology overview (brief)
- Paper structure roadmap

ACADEMIC STANDARDS:
- Follow ${requirements.citationStyle?.toUpperCase() || "APA"} citation format
- Use formal, scholarly language throughout
- Maintain objective, evidence-based tone
- Include proper academic transitions
- Establish theoretical framework foundation
- Ensure content depth matches ${academicLevelText} expectations

Length: 500-800 words
Tone: Formal academic, objective, scholarly

Generate the complete academic introduction in markdown format:`;
  }

  if (promptType === "conclusion") {
    return `You are an expert academic researcher and scholarly writer. Write a comprehensive conclusion for your ${
      subType || "academic paper"
    }:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Academic Field: Determine appropriate field based on topic
Target Audience: ${baseContext.audience}
Academic Level: ${academicLevelText}
Writing Context: ${academicLevelContext}
Citation Style: ${requirements.citationStyle?.toUpperCase() || "APA"}

ACADEMIC CONCLUSION REQUIREMENTS:
- Summarize key research findings and their significance
- Restate thesis and demonstrate how it was supported
- Discuss implications for the field and broader applications
- Address limitations of the current research
- Suggest directions for future research
- Conclude with strong statement of contribution to knowledge
- Maintain scholarly objectivity throughout
- Tailor language complexity and theoretical depth to ${academicLevelText} readership

STRICT ACADEMIC LANGUAGE STANDARDS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's explore"
- AVOID explanatory phrases like "this chapter explains", "this section covers"
- DO NOT use conversational language or second-person ("you", "your")
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, scholarly language with precise academic terminology
- MAINTAIN objective, third-person perspective throughout
- PRESENT arguments with evidence-based support without informal narrative

CONCLUSION STRUCTURE:
- Restatement of research purpose and thesis
- Summary of key findings and evidence
- Discussion of implications and significance
- Acknowledgment of research limitations
- Suggestions for future research directions
- Final statement of contribution to field

ACADEMIC STANDARDS:
- Use formal, scholarly language and third-person perspective
- Follow ${requirements.citationStyle?.toUpperCase() || "APA"} citation format
- Maintain objective, evidence-based tone
- Include relevant citations where appropriate
- Demonstrate critical thinking and analysis
- Ensure content depth matches ${academicLevelText} expectations

STRICT ACADEMIC LANGUAGE STANDARDS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's explore"
- AVOID explanatory phrases like "this chapter explains", "this section covers"
- DO NOT use conversational language or second-person ("you", "your")
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, scholarly language with precise academic terminology
- MAINTAIN objective, third-person perspective throughout
- PRESENT arguments with evidence-based support without informal narrative

Length: 400-600 words
Tone: Formal academic, objective, conclusive

Generate the complete academic conclusion in markdown format:`;
  }

  // Add more academic prompt types as needed
  return `Academic ${promptType} prompt for ${subType} - to be implemented`;
};

/**
 * Placeholder for generic prompt when no specific type is found
 * @param {Object} documentData - Document configuration
 * @param {string} promptType - Type of prompt
 * @returns {string} Generic prompt
 */
const createGenericPrompt = (documentData, promptType, chapterData) => {
  const baseContext = extractBaseContext(documentData);

  if (promptType === "sub-topics") {
    return `You are an expert content strategist and niche specialist. Generate 8 highly relevant and specific sub-niches for the topic "${
      baseContext.topic
    }".

Requirements:
- Each sub-niche should be specific and actionable
- Focus on areas that would make excellent content topics
- Consider different skill levels and approaches
- Make them appealing to content creators and audiences
- Ensure they are distinct from each other

Return ONLY a valid JSON array with objects containing:
- id: kebab-case identifier (e.g., "wellness-coaching")
- name: Clear, descriptive name (e.g., "Wellness and Health Coaching")
- description: Brief explanation of what this sub-niche covers (max 60 characters)

Topic: ${baseContext.topic}
Language: ${baseContext.language || "english"}

JSON Response:`;
  }

  return `You are an expert content strategist and writer. Create a comprehensive ${promptType} for the following specifications:

Topic: ${baseContext.topic}
Target Audience: ${baseContext.audience}
Tone: ${baseContext.tone}

Requirements:
- Create well-structured content appropriate for the audience
- Maintain consistent tone throughout
- Ensure comprehensive coverage of the topic
- Make it practical and valuable

Return appropriate JSON structure for ${promptType}.`;
};

/**
 * Create business document prompts (proposals, market analysis, business plans)
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Business requirements
 * @param {string} subType - Business sub-type (market-analysis, business-plan, etc.)
 * @param {string} promptType - Type of prompt to generate
 * @returns {string} Business-specific AI prompt
 */
const createBusinessPrompt = (
  documentData,
  requirements,
  subType,
  promptType,
  chapterData
) => {
  const baseContext = extractBaseContext(documentData);
  const businessSections = getBusinessSections(subType);

  if (promptType === "sub-topics") {
    return `You are an expert business strategist and management consultant. Generate 8 highly relevant key focus areas for the business topic "${
      baseContext.topic
    }".

BUSINESS CONTEXT:
- Document Title: ${baseContext.title}
- Document Type: Business ${subType || "Report"}
- Business Domain: ${getBusinessContext(baseContext.topic)}
- Target Audience: ${baseContext.audience}
- Objective: Strategic decision-making and ROI analysis

KEY FOCUS REQUIREMENTS:
- Each focus area should represent a distinct business domain or strategic angle
- Focus on areas that would drive business value and competitive advantage
- Consider different market segments, operational areas, and strategic initiatives
- Ensure they are actionable and measurable
- Make them specific enough for detailed business analysis
- Ensure they are distinct from each other

Return ONLY a valid JSON array with objects containing:
- id: kebab-case identifier (e.g., "market-penetration-strategy")
- name: Clear, business name (e.g., "Market Penetration Strategy")
- description: Brief explanation of the business focus (max 80 characters)

Topic: ${baseContext.topic}
Language: ${baseContext.language || "english"}

JSON Response:`;
  }

  if (promptType === "outline") {
    return `You are an expert business analyst and management consultant with extensive experience in ${
      baseContext.topic
    }. Create a comprehensive ${
      subType || "business report"
    } for C-level executives and stakeholders.

DOCUMENT SPECIFICATIONS:
- Type: Business ${subType || "Report"}
- Title: ${baseContext.title}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Business Context: Strategic decision-making and ROI analysis

BUSINESS REQUIREMENTS:
- Executive Summary: ${
      requirements.includeExecutiveSummary
        ? "MANDATORY - Maximum 2 pages with key findings and ROI"
        : "Optional"
    }
- Visual Elements: ${
      requirements.visualElements?.includeCharts
        ? "Include charts, graphs, and data visualizations"
        : "Text-focused with minimal visuals"
    }
- Organization Style: ${
      requirements.organizationStyle ||
      "Logical progression with clear recommendations"
    }
- Professional Tone: Confident, decisive, action-oriented language
- ROI Focus: Emphasize return on investment and business value

REQUIRED BUSINESS SECTIONS:
${businessSections
  .map(
    (section) =>
      `- ${section.name}: ${section.description} (${section.wordCount})`
  )
  .join("\n")}

BUSINESS STANDARDS:
- Lead with executive summary highlighting key recommendations and ROI
- Include specific KPIs, metrics, and measurable outcomes
- Focus on actionable recommendations with clear next steps
- Use confident, decisive language appropriate for executives
- Structure for board presentation and strategic decision-making
- Include competitive analysis and market positioning
- Provide implementation timeline with resource requirements
- Address risk assessment and mitigation strategies

STRICT LANGUAGE STANDARDS FOR BUSINESS DOCUMENTS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's dive into"
- AVOID explanatory phrases like "this chapter explains", "this section covers"
- DO NOT use conversational language or second-person ("you", "your")
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, declarative statements that present facts and analysis
- MAINTAIN executive-level professionalism throughout
- PRESENT information directly without unnecessary narrative fluff

CONTENT REQUIREMENTS:
- Each section must drive toward business value and ROI
- Include data-driven insights and market research
- Provide specific recommendations with cost-benefit analysis
- Use professional business terminology and frameworks
- Include implementation roadmap with clear milestones
- Address stakeholder concerns and decision criteria

FORMATTING REQUIREMENTS:
- Use bullet points and lists for clarity
- Include executive-level summaries for each major section
- Provide clear headings and subheadings for easy navigation
- Include space for charts, graphs, and visual data representation

Return ONLY a valid JSON object with this structure:
{
  "title": "Business document title",
  "executiveSummary": "Brief executive summary with key findings and ROI (200-400 words)",
  "chapters": [
    {
      "number": 1,
      "title": "Section title",
      "sections": ["Subsection 1", "Subsection 2", "Subsection 3"],
      "wordCount": "estimated word count",
      "businessFocus": "specific business value or outcome",
      "keyMetrics": ["relevant KPIs or metrics to include"]
    }
  ],
  "requiredElements": ["executive summary", "recommendations", "implementation plan"],
  "businessType": "${subType || "report"}",
  "targetAudience": "C-level executives"
}

JSON Response:`;
  }

  if (promptType === "title") {
    // Subtype-specific title generation
    const subtypeContext = getBusinessSubtypeContext(subType);

    return `You are an expert business strategist and executive communications specialist. Generate 5 compelling business document titles for a ${
      subType || "business report"
    }:

Topic: ${baseContext.topic}
Business Context: ${getBusinessContext(baseContext.topic)}
Target Audience: ${baseContext.audience}
Document Purpose: ${subtypeContext.purpose}
Document Subtype: ${subType || "business report"}${
      baseContext.subNiches && baseContext.subNiches.length > 0
        ? `
Business Focus Areas: ${baseContext.subNiches
            .map((s) => s.name || s)
            .join(", ")}`
        : ""
    }

${subtypeContext.titleRequirements}

BUSINESS TITLE REQUIREMENTS:
- Use professional business language that commands executive attention
- Include power words that convey value and urgency
- Focus on business outcomes and ROI implications
- Length: ${subtypeContext.titleLength}
- Avoid jargon - use clear, decisive language
- Include action-oriented language when appropriate

TITLE STYLES TO INCLUDE:
${subtypeContext.titleStyles}

BUSINESS POWER WORDS TO CONSIDER:
- Strategic, Executive, Comprehensive, Analysis, Insights
- ROI, Growth, Optimization, Transformation, Innovation
- Market, Competitive, Performance, Success, Results

Return ONLY a valid JSON array with objects containing:
- id: unique identifier
- text: the business title
- style: one of "strategic", "analytical", "executive", "action-oriented"
- businessFocus: primary business value proposition

JSON Response:`;
  }

  if (promptType === "chapter" && chapterData) {
    return `You are an expert business analyst and management consultant. Generate comprehensive content for this business section:

${chapterData.title}

BUSINESS CONTEXT:
- Document Title: ${baseContext.title}
- Document Type: Business ${subType || "Report"}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Business Focus: ${getBusinessContext(baseContext.topic)}
- Objective: Strategic decision-making and ROI analysis

CHAPTER SECTIONS TO COVER:
${
  chapterData.sections?.map((section) => `- ${section}`).join("\n") ||
  "- Main content for this chapter"
}

BUSINESS WRITING REQUIREMENTS:
- Use professional business language and confident tone
- Focus on actionable insights and recommendations
- Include relevant data, metrics, and KPIs where appropriate
- Provide specific implementation strategies and timelines
- Address ROI implications and business value
- Use executive-level language appropriate for C-suite presentation
- Include competitive analysis and market positioning where relevant

STRICT LANGUAGE STANDARDS FOR BUSINESS DOCUMENTS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's dive into"
- AVOID explanatory phrases like "this chapter explains", "this section covers"
- DO NOT use conversational language or second-person ("you", "your") 
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, declarative statements that present facts and analysis
- MAINTAIN executive-level professionalism throughout
- PRESENT information directly without unnecessary narrative fluff

DATA PRESENTATION REQUIREMENTS:
- Present metrics, KPIs, and data using bullet points or numbered lists instead of tables
- Use structured lists for financial data, comparisons, and statistical information
- Format complex data as clear, organized paragraphs with headings
- Avoid markdown table syntax (pipes |) - use executive-friendly list formatting instead

CONTENT STRUCTURE:
- Begin with clear business case and value proposition
- Develop arguments with supporting data and market research
- Include specific recommendations with cost-benefit analysis
- Provide implementation roadmap with clear milestones
- Address potential risks and mitigation strategies
- Conclude with measurable outcomes and success metrics

LENGTH: 800-1200 words
TONE: Professional, confident, results-oriented, executive-level

Generate the complete business chapter content in markdown format:`;
  }

  // Handle other prompt types for business documents
  return createBusinessContentPrompt(
    documentData,
    requirements,
    subType,
    promptType,
    chapterData
  );
};

/**
 * Get business section structure based on sub-type
 * @param {string} subType - Business document sub-type
 * @returns {Array} Array of section objects with business requirements
 */
const getBusinessSections = (subType) => {
  const sections = {
    "market-analysis": [
      {
        name: "Executive Summary",
        description: "Key findings, market size, and strategic recommendations",
        wordCount: "500-800 words",
      },
      {
        name: "Market Overview",
        description: "Market size, growth trends, and key drivers",
        wordCount: "1000-1500 words",
      },
      {
        name: "Competitive Landscape",
        description:
          "Key players, market positioning, and competitive advantages",
        wordCount: "1500-2000 words",
      },
      {
        name: "Market Segmentation",
        description:
          "Target segments, customer profiles, and market opportunities",
        wordCount: "1000-1500 words",
      },
      {
        name: "Trends & Opportunities",
        description: "Emerging trends, market gaps, and growth opportunities",
        wordCount: "1000-1500 words",
      },
      {
        name: "Strategic Recommendations",
        description: "Actionable recommendations with ROI projections",
        wordCount: "800-1200 words",
      },
      {
        name: "Implementation Roadmap",
        description: "Timeline, milestones, and resource requirements",
        wordCount: "600-1000 words",
      },
      {
        name: "Risk Assessment",
        description: "Potential challenges and mitigation strategies",
        wordCount: "400-600 words",
      },
      {
        name: "Appendices",
        description: "Supporting data, charts, and detailed analysis",
        wordCount: "varies",
      },
    ],
    "business-plan": [
      {
        name: "Executive Summary",
        description:
          "Business concept, financial highlights, and funding requirements",
        wordCount: "800-1200 words",
      },
      {
        name: "Company Description",
        description: "Mission, vision, values, and business model",
        wordCount: "600-1000 words",
      },
      {
        name: "Market Analysis",
        description:
          "Industry overview, target market, and competitive analysis",
        wordCount: "1500-2500 words",
      },
      {
        name: "Organization & Management",
        description: "Organizational structure and management team",
        wordCount: "800-1200 words",
      },
      {
        name: "Products & Services",
        description: "Detailed description of offerings and value proposition",
        wordCount: "1000-1500 words",
      },
      {
        name: "Marketing & Sales Strategy",
        description: "Customer acquisition and revenue generation strategy",
        wordCount: "1200-1800 words",
      },
      {
        name: "Financial Projections",
        description:
          "Revenue forecasts, expense projections, and profitability analysis",
        wordCount: "1000-1500 words",
      },
      {
        name: "Funding Requirements",
        description: "Capital needs, use of funds, and exit strategy",
        wordCount: "600-1000 words",
      },
      {
        name: "Risk Analysis",
        description: "Business risks and contingency planning",
        wordCount: "400-800 words",
      },
    ],
    proposal: [
      {
        name: "Executive Summary",
        description: "Project overview, benefits, and recommended solution",
        wordCount: "400-600 words",
      },
      {
        name: "Problem Statement",
        description: "Clear definition of business challenge or opportunity",
        wordCount: "600-1000 words",
      },
      {
        name: "Proposed Solution",
        description: "Detailed solution approach and methodology",
        wordCount: "1200-1800 words",
      },
      {
        name: "Implementation Plan",
        description: "Project timeline, phases, and deliverables",
        wordCount: "1000-1500 words",
      },
      {
        name: "Team & Qualifications",
        description: "Project team expertise and relevant experience",
        wordCount: "800-1200 words",
      },
      {
        name: "Budget & Pricing",
        description: "Detailed cost breakdown and pricing structure",
        wordCount: "600-1000 words",
      },
      {
        name: "Expected Outcomes",
        description: "Measurable benefits and ROI projections",
        wordCount: "600-1000 words",
      },
      {
        name: "Terms & Conditions",
        description: "Project terms, assumptions, and next steps",
        wordCount: "400-600 words",
      },
    ],
    "feasibility-study": [
      {
        name: "Executive Summary",
        description: "Study purpose, key findings, and recommendations",
        wordCount: "600-1000 words",
      },
      {
        name: "Project Description",
        description: "Detailed project scope and objectives",
        wordCount: "800-1200 words",
      },
      {
        name: "Market Feasibility",
        description: "Market demand, competition, and customer analysis",
        wordCount: "1500-2000 words",
      },
      {
        name: "Technical Feasibility",
        description: "Technical requirements, resources, and capabilities",
        wordCount: "1200-1800 words",
      },
      {
        name: "Financial Feasibility",
        description: "Cost analysis, revenue projections, and ROI assessment",
        wordCount: "1500-2000 words",
      },
      {
        name: "Risk Assessment",
        description: "Potential risks, challenges, and mitigation strategies",
        wordCount: "1000-1500 words",
      },
      {
        name: "Recommendations",
        description: "Go/no-go decision with supporting rationale",
        wordCount: "600-1000 words",
      },
      {
        name: "Implementation Considerations",
        description: "Next steps and implementation requirements",
        wordCount: "400-800 words",
      },
    ],
  };

  return sections[subType] || sections["market-analysis"];
};

/**
 * Get business context based on topic
 * @param {string} topic - Business topic
 * @returns {string} Business context description
 */
const getBusinessContext = (topic) => {
  const contexts = {
    marketing: "Digital marketing strategy and customer acquisition",
    finance: "Financial analysis and investment decision-making",
    operations: "Operational efficiency and process optimization",
    strategy: "Strategic planning and competitive positioning",
    technology: "Technology implementation and digital transformation",
    sales: "Sales strategy and revenue optimization",
    hr: "Human resources and organizational development",
    consulting: "Management consulting and business advisory",
  };

  // Try to match topic with business context
  const lowerTopic = topic.toLowerCase();
  for (const [key, context] of Object.entries(contexts)) {
    if (lowerTopic.includes(key)) {
      return context;
    }
  }

  return "Strategic business analysis and decision-making";
};

/**
 * Get subtype-specific context for business documents
 * @param {string} subType - Business subtype
 * @returns {Object} Subtype-specific context
 */
const getBusinessSubtypeContext = (subType) => {
  const contexts = {
    proposal: {
      purpose: "Persuasive business proposal to secure approval or funding",
      titleRequirements: `PROPOSAL-SPECIFIC TITLE REQUIREMENTS:
- Use persuasive language: "Strategic Partnership Proposal", "Investment Opportunity"
- Include value proposition: "Maximizing ROI", "Driving Growth"
- Focus on benefits and outcomes
- Use action-oriented language: "Transforming", "Optimizing", "Accelerating"`,
      titleLength: "6-10 words optimal for proposal format",
      titleStyles: `- Value-focused: "[Solution]: Maximizing [Benefit] for [Organization]"
- Opportunity-based: "Strategic Opportunity: [Specific Focus]"
- Partnership-oriented: "Partnership Proposal: [Collaboration Type]"
- ROI-focused: "Driving [Outcome]: A Strategic Proposal"`,
    },
    report: {
      purpose: "Analytical business report for informed decision-making",
      titleRequirements: `REPORT-SPECIFIC TITLE REQUIREMENTS:
- Use analytical language: "Analysis", "Assessment", "Review", "Evaluation"
- Include scope and timeframe: "Q4 2024", "Annual", "Market Analysis"
- Focus on findings and insights
- Use data-driven terminology: "Performance", "Metrics", "Trends"`,
      titleLength: "8-12 words optimal for report format",
      titleStyles: `- Analytical: "[Topic] Analysis: Key Findings and Recommendations"
- Performance-based: "[Department/Area] Performance Report: [Time Period]"
- Market-focused: "Market Analysis: [Industry/Sector] Trends and Opportunities"
- Assessment-oriented: "Strategic Assessment of [Topic/Initiative]"`,
    },
    "business-plan": {
      purpose:
        "Comprehensive business plan for strategic planning and investment",
      titleRequirements: `BUSINESS PLAN-SPECIFIC TITLE REQUIREMENTS:
- Include company/venture name when applicable
- Use growth-oriented language: "Growth Strategy", "Business Development"
- Focus on vision and market opportunity
- Include timeframe: "2024-2027", "Five-Year Plan"`,
      titleLength: "8-14 words optimal for business plan format",
      titleStyles: `- Vision-based: "[Company/Venture]: Strategic Business Plan 2024-2027"
- Market-focused: "Capturing [Market] Opportunity: Business Development Plan"
- Growth-oriented: "[Business] Growth Strategy and Implementation Plan"
- Investment-ready: "Business Plan: [Company] Investment Opportunity"`,
    },
    "executive-summary": {
      purpose: "High-level executive summary for C-level decision-making",
      titleRequirements: `EXECUTIVE SUMMARY-SPECIFIC TITLE REQUIREMENTS:
- Use executive language: "Executive Overview", "Strategic Summary"
- Include "Executive Summary" in title for clarity
- Focus on high-level outcomes and decisions
- Use concise, powerful language: "Key Insights", "Strategic Recommendations"`,
      titleLength: "6-10 words optimal for executive format",
      titleStyles: `- Overview-based: "Executive Summary: [Topic] Strategic Overview"
- Decision-focused: "[Initiative] Executive Summary: Key Decisions"
- Insight-oriented: "Strategic Insights: [Topic] Executive Summary"
- Recommendation-based: "Executive Summary: [Topic] Recommendations"`,
    },
  };

  return contexts[subType] || contexts["report"];
};

/**
 * Create business content prompts for specific sections
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - Business requirements
 * @param {string} subType - Business sub-type
 * @param {string} promptType - Specific prompt type
 * @returns {string} Business content prompt
 */
const createBusinessContentPrompt = (
  documentData,
  requirements,
  subType,
  promptType
) => {
  const baseContext = extractBaseContext(documentData);

  if (promptType === "introduction") {
    return `You are an expert business analyst. Write a compelling introduction for a ${
      subType || "business report"
    }:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Business Context: ${getBusinessContext(baseContext.topic)}
Target Audience: ${baseContext.audience}

BUSINESS INTRODUCTION REQUIREMENTS:
- Hook executives with compelling business case
- Clearly state the business problem or opportunity
- Outline key benefits and ROI potential
- Set expectations for strategic recommendations
- Use confident, professional business language
- Include relevant market context and urgency

STRICT LANGUAGE STANDARDS FOR BUSINESS DOCUMENTS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's dive into"
- AVOID explanatory phrases like "this chapter explains", "this section covers"  
- DO NOT use conversational language or second-person ("you", "your")
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, declarative statements that present facts and analysis
- MAINTAIN executive-level professionalism throughout
- PRESENT information directly without unnecessary narrative fluff

STRUCTURE:
- Opening hook with business impact
- Problem/opportunity statement
- Document purpose and scope
- Key benefits preview
- Document roadmap

Length: 400-600 words
Tone: Professional, confident, results-oriented

Generate the business introduction in markdown format:`;
  }

  if (promptType === "chapter" && chapterData) {
    return `You are an expert business analyst and management consultant. Generate comprehensive content for this business section:

${chapterData.title}

BUSINESS CONTEXT:
- Document Title: ${baseContext.title}
- Document Type: Business ${subType || "Report"}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Business Focus: ${getBusinessContext(baseContext.topic)}
- Objective: Strategic decision-making and ROI analysis

CHAPTER SECTIONS TO COVER:
${
  chapterData.sections?.map((section) => `- ${section}`).join("\n") ||
  "- Main content for this chapter"
}

BUSINESS WRITING REQUIREMENTS:
- Use professional business language and confident tone
- Focus on actionable insights and recommendations
- Include relevant data, metrics, and KPIs where appropriate
- Emphasize ROI and business value propositions
- Provide clear implementation guidance
- Use executive-level communication style
- Include competitive analysis and market insights

STRICT LANGUAGE STANDARDS FOR BUSINESS DOCUMENTS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's dive into"
- AVOID explanatory phrases like "this chapter explains", "this section covers"
- DO NOT use conversational language or second-person ("you", "your")
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, declarative statements that present facts and analysis
- MAINTAIN executive-level professionalism throughout
- PRESENT information directly without unnecessary narrative fluff

DATA PRESENTATION REQUIREMENTS:
- Present financial data, metrics, and KPIs using structured bullet points or numbered lists
- Format comparison data as clear, organized lists rather than tables
- Use descriptive paragraphs with clear headings for complex data analysis
- Avoid markdown table syntax (pipes |) - use professional list formatting for readability

CONTENT STRUCTURE:
- Begin with clear business case or problem statement
- Develop arguments with supporting data and market research
- Include specific recommendations with cost-benefit analysis
- Provide implementation roadmap and resource requirements
- Conclude with measurable outcomes and success metrics
- Use professional transitions and business frameworks

LENGTH: 800-1200 words
TONE: Professional, confident, results-oriented, executive-level

Generate the complete business chapter content in markdown format:`;
  }

  if (promptType === "conclusion") {
    return `You are an expert business analyst and management consultant. Write a powerful conclusion for your ${
      subType || "business report"
    }:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Business Context: ${getBusinessContext(baseContext.topic)}
Target Audience: ${baseContext.audience}
Document Purpose: Strategic business analysis and decision-making

BUSINESS CONCLUSION REQUIREMENTS:
- Summarize key findings and strategic insights
- Present clear, actionable recommendations with ROI implications
- Provide implementation roadmap with timelines and resources
- Address potential risks and mitigation strategies
- Include measurable success metrics and KPIs
- End with compelling call-to-action for stakeholders
- Use confident, decisive executive language

CONCLUSION STRUCTURE:
- Executive summary of key findings
- Strategic recommendations with business impact
- Implementation plan with resource requirements
- Risk assessment and mitigation strategies
- Success metrics and measurement framework
- Next steps and call-to-action

BUSINESS STANDARDS:
- Focus on ROI and business value propositions
- Use professional, confident language appropriate for executives
- Include specific, measurable outcomes
- Provide clear implementation guidance
- Address stakeholder concerns and decision criteria
- Emphasize competitive advantage and market positioning

STRICT LANGUAGE STANDARDS FOR BUSINESS DOCUMENTS:
- NEVER use informal phrases like "imagine you're", "picture this", "let's dive into"
- AVOID explanatory phrases like "this chapter explains", "this section covers"
- DO NOT use conversational language or second-person ("you", "your")
- ELIMINATE casual expressions, contractions, or colloquial terms
- USE formal, declarative statements that present facts and analysis
- MAINTAIN executive-level professionalism throughout
- PRESENT information directly without unnecessary narrative fluff

Length: 400-600 words
Tone: Professional, confident, results-oriented, decisive

Generate the complete business conclusion in markdown format:`;
  }

  // Add more business prompt types as needed
  return `Business ${promptType} prompt for ${subType} - to be implemented`;
};

// Report functionality removed - only supporting ebook, academic, business

/**
 * Create eBook document prompts (fiction, non-fiction, self-help, educational)
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - eBook requirements
 * @param {string} subType - eBook sub-type (fiction, non-fiction, self-help, etc.)
 * @param {string} promptType - Type of prompt to generate
 * @param {Object} chapterData - Chapter-specific data for content generation
 * @returns {string} eBook-specific AI prompt
 */
const createEbookPrompt = (
  documentData,
  requirements,
  subType,
  promptType,
  chapterData
) => {
  const baseContext = extractBaseContext(documentData);
  const ebookSections = getEbookSections(subType);

  if (promptType === "sub-topics") {
    return `You are an expert eBook author and content strategist. Generate 8 highly relevant sub-niches for the eBook topic "${
      baseContext.topic
    }".

EBOOK CONTEXT:
- Type: ${subType || "eBook"}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Publishing Format: Digital (PDF)
- Reading Experience: ${getEbookReadingContext(subType)}

SUB-NICHE REQUIREMENTS:
- Each sub-niche should represent a distinct content angle or reader interest
- Focus on areas that would make excellent eBook content
- Consider different reader motivations and skill levels
- Ensure they are marketable and appealing to your target audience
- Make them specific enough for focused content creation
- Ensure they are distinct from each other

Return ONLY a valid JSON array with objects containing:
- id: kebab-case identifier (e.g., "beginner-fitness-routines")
- name: Clear, marketable name (e.g., "Beginner Fitness Routines")
- description: Brief explanation of the sub-niche appeal (max 80 characters)

Topic: ${baseContext.topic}
Language: ${baseContext.language || "english"}

JSON Response:`;
  }

  if (promptType === "outline") {
    return `You are an expert eBook author and publishing strategist specializing in ${
      baseContext.topic
    }. Create a comprehensive ${
      subType || "eBook"
    } outline that will engage readers and deliver exceptional value.

EBOOK SPECIFICATIONS:
- Type: ${subType || "eBook"}
- Title: ${baseContext.title}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Publishing Format: Digital (PDF)
- Reading Experience: ${getEbookReadingContext(subType)}

EBOOK REQUIREMENTS:
- Narrative Structure: ${
      requirements.narrativeStructure
        ? "Strong story arc with compelling progression"
        : "Logical information flow"
    }
- Chapter Organization: Sequential chapters that build upon each other
- Reader Engagement: High engagement with hooks, cliffhangers, and compelling content
- Publishing Considerations: Optimized for digital reading with proper formatting
- Table of Contents: Clear navigation structure for digital readers
- Target Length: ${getEbookTargetLength(subType)}

REQUIRED EBOOK SECTIONS:
${ebookSections
  .map(
    (section) =>
      `- ${section.name}: ${section.description} (${section.wordCount})`
  )
  .join("\n")}

EBOOK WRITING STANDARDS:
- Create compelling chapter titles that entice readers to continue
- Develop strong opening hooks and satisfying chapter conclusions
- Maintain consistent voice and tone throughout the book
- Include practical examples, stories, or case studies where appropriate
- Design content for optimal digital reading experience
- Consider chapter length for comfortable reading sessions (2000-4000 words)
- Include clear transitions between chapters and sections
- Optimize for searchability and digital bookmarking

READER ENGAGEMENT TECHNIQUES:
- Start each chapter with a compelling hook or question
- Use storytelling techniques to illustrate key points
- Include actionable takeaways and practical applications
- Create emotional connection with readers through relatable examples
- End chapters with curiosity gaps or cliffhangers when appropriate
- Use formatting that enhances digital reading (headers, bullet points, etc.)

PUBLISHING FORMAT CONSIDERATIONS:
- Structure content for easy navigation in digital formats
- Consider how content will appear on various devices (phones, tablets, e-readers)
- Include appropriate chapter breaks and section divisions
- Plan for interactive elements where applicable (links, references)

Return ONLY a valid JSON object with this structure:
{
  "title": "Compelling eBook title",
  "subtitle": "Engaging subtitle that clarifies value proposition",
  "description": "Brief description of what readers will gain (100-200 words)",
  "targetAudience": "Specific reader demographics and interests",
  "chapters": [
    {
      "number": 1,
      "title": "Engaging chapter title",
      "sections": ["Section 1", "Section 2", "Section 3"],
      "wordCount": "estimated word count",
      "chapterPurpose": "what this chapter accomplishes for the reader",
      "keyTakeaways": ["main learning point 1", "main learning point 2"],
      "engagementHook": "how this chapter hooks the reader"
    }
  ],
  "requiredElements": ["table of contents", "introduction", "conclusion", "about the author"],
  "ebookType": "${subType || "general"}",
  "readingLevel": "appropriate reading level",
  "estimatedReadingTime": "total reading time",
  "publishingFormat": "digital optimization notes"
}

JSON Response:`;
  }

  if (promptType === "title") {
    return `You are an expert eBook author and digital publishing strategist. Generate 8 compelling eBook titles for a ${
      subType || "eBook"
    }:

Topic: ${baseContext.topic}
Target Audience: ${baseContext.audience}
eBook Purpose: ${getEbookPurpose(subType)}
Reading Context: ${getEbookReadingContext(subType)}${
      baseContext.subNiches && baseContext.subNiches.length > 0
        ? `
Sub-niches Focus: ${baseContext.subNiches.map((s) => s.name || s).join(", ")}`
        : ""
    }

EBOOK TITLE REQUIREMENTS:
- Create titles that stand out in digital marketplaces (Amazon, Apple Books, etc.)
- Use emotional triggers and benefit-driven language
- Include power words that convey transformation and value
- Length: 3-8 words optimal for digital display and searchability
- Consider SEO and discoverability in digital bookstores
- Appeal to the specific motivations of your target audience
- Avoid overly generic or clichéd phrases

TITLE STYLES TO INCLUDE:
- Benefit-Driven: Focus on what readers will gain or achieve
- Problem-Solution: Address specific pain points and solutions
- Transformation: Emphasize change and improvement
- Authority: Position as definitive guide or expert resource

EBOOK POWER WORDS TO CONSIDER:
- Complete, Ultimate, Essential, Proven, Secret, Mastery
- Transform, Discover, Unlock, Master, Achieve, Success
- Guide, Blueprint, System, Method, Strategy, Formula
- ${getEbookKeywords(subType)}

DIGITAL PUBLISHING CONSIDERATIONS:
- Titles should be compelling in thumbnail view
- Consider how titles appear in search results
- Think about subtitle opportunities for clarification
- Ensure titles work across different digital platforms

Return ONLY a valid JSON array with objects containing:
- id: unique identifier
- text: the eBook title
- style: one of "benefit-driven", "problem-solution", "transformation", "authority"
- marketingHook: primary appeal to target audience
- digitalOptimization: how this title performs in digital marketplaces

JSON Response:`;
  }

  if (promptType === "introduction") {
    return `You are an expert eBook author specializing in ${
      subType || "digital publishing"
    }. Write a compelling introduction for your eBook:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Target Audience: ${baseContext.audience}
eBook Type: ${subType || "General eBook"}

EBOOK INTRODUCTION REQUIREMENTS:
- Hook readers immediately with a compelling opening
- Clearly establish the value proposition and what readers will gain
- Create emotional connection with your target audience
- Set expectations for the reading journey ahead
- Include personal credibility or story if appropriate
- Address the specific problem or opportunity your book solves
- Create excitement and anticipation for the content to come

INTRODUCTION STRUCTURE:
- Opening hook that captures attention immediately
- Problem identification that resonates with readers
- Promise of transformation or solution
- Brief overview of what's covered (without spoilers)
- Personal connection or credibility establishment
- Call to action to continue reading

ENGAGEMENT TECHNIQUES:
- Use storytelling to illustrate key points
- Include relatable scenarios or examples
- Ask thought-provoking questions
- Create curiosity gaps that compel continued reading
- Use conversational, accessible language
- Build anticipation for the value to come

Length: 500-800 words
Tone: ${getEbookTone(subType)}

Generate the complete eBook introduction in markdown format:`;
  }

  if (promptType === "chapter" && chapterData) {
    return `You are an expert eBook author and content strategist. Generate compelling content for this eBook chapter:

Chapter ${chapterData.number}: ${chapterData.title}

EBOOK CONTEXT:
- Document Title: ${baseContext.title}
- Document Type: ${subType || "eBook"}
- Topic: ${baseContext.topic}
- Target Audience: ${baseContext.audience}
- Reading Experience: ${getEbookReadingContext(subType)}
- Chapter Purpose: ${
      chapterData.chapterPurpose ||
      "Advance the reader's understanding and engagement"
    }

CHAPTER SECTIONS TO COVER:
${
  chapterData.sections?.map((section) => `- ${section}`).join("\n") ||
  "- Main chapter content"
}

KEY TAKEAWAYS FOR THIS CHAPTER:
${
  chapterData.keyTakeaways?.map((takeaway) => `- ${takeaway}`).join("\n") ||
  "- Primary learning objectives"
}

EBOOK WRITING REQUIREMENTS:
- Create engaging, readable content optimized for digital consumption
- Use storytelling techniques and real-world examples
- Maintain consistent voice and tone throughout
- Include practical applications and actionable insights
- Design content for comfortable digital reading sessions
- Use formatting that enhances readability (headers, bullet points, etc.)
- Create smooth transitions between sections
- End with compelling hooks or cliffhangers when appropriate

CONTENT FORMATTING REQUIREMENTS:
- Present information using engaging bullet points and numbered lists instead of tables
- Format data and comparisons as reader-friendly structured lists
- Use descriptive paragraphs with clear subheadings for complex information
- Avoid markdown table syntax (pipes |) - use narrative-friendly list formatting for better digital reading experience

CONTENT STRUCTURE:
- Begin with engaging hook or chapter preview
- Develop content with clear progression and logical flow
- Include practical examples, case studies, or stories
- Provide actionable takeaways and implementation guidance
- Use subheadings and formatting for easy scanning
- Conclude with chapter summary and transition to next chapter

READER ENGAGEMENT TECHNIQUES:
- Use conversational, accessible language
- Include relatable examples and scenarios
- Ask rhetorical questions to maintain engagement
- Create emotional connection through storytelling
- Provide immediate value and practical insights
- Use formatting to break up text and improve readability

DIGITAL READING OPTIMIZATION:
- Structure content for various screen sizes
- Use appropriate paragraph lengths for digital reading
- Include clear section breaks and transitions
- Consider how content flows on different devices
- Optimize for both linear and non-linear reading patterns

LENGTH: 2000-4000 words (optimal for digital reading sessions)
TONE: ${getEbookTone(subType)}

Generate the complete eBook chapter content in markdown format:`;
  }

  if (promptType === "conclusion") {
    return `You are an expert eBook author and publishing strategist. Write a powerful conclusion for your ${
      subType || "eBook"
    }:

Title: ${baseContext.title}
Topic: ${baseContext.topic}
Target Audience: ${baseContext.audience}
eBook Type: ${subType || "General eBook"}
Reading Experience: ${getEbookReadingContext(subType)}

EBOOK CONCLUSION REQUIREMENTS:
- Provide satisfying closure to the reader's journey
- Reinforce key takeaways and value delivered
- Inspire readers to take action on what they've learned
- Include practical next steps for continued growth
- Create emotional connection and lasting impact
- Encourage readers to share or recommend the book
- End with memorable final thought or call-to-action

CONCLUSION STRUCTURE:
- Acknowledgment of reader's commitment and progress
- Summary of key insights and transformations achieved
- Reinforcement of main value propositions
- Practical next steps and implementation guidance
- Additional resources for continued learning
- Inspirational closing message
- Clear call-to-action for engagement

READER ENGAGEMENT TECHNIQUES:
- Use personal, conversational language that connects
- Include success stories or transformation examples
- Provide specific, actionable next steps
- Create sense of community and ongoing support
- Use motivational language that inspires action
- Include ways for readers to stay connected

DIGITAL PUBLISHING CONSIDERATIONS:
- Include links to additional resources where appropriate
- Consider follow-up content or sequel opportunities
- Encourage reviews and social sharing
- Provide contact information for further engagement
- Optimize for digital reading completion satisfaction

Length: 500-800 words
Tone: ${getEbookTone(subType)}, inspirational, actionable

Generate the complete eBook conclusion in markdown format:`;
  }

  // Handle other prompt types for eBook documents
  return createEbookContentPrompt(
    documentData,
    requirements,
    subType,
    promptType
  );
};

/**
 * Get eBook section structure based on sub-type
 * @param {string} subType - eBook document sub-type
 * @returns {Array} Array of section objects with eBook requirements
 */
const getEbookSections = (subType) => {
  const sections = {
    fiction: [
      {
        name: "Prologue/Opening",
        description: "Compelling opening that hooks readers immediately",
        wordCount: "1000-2000 words",
      },
      {
        name: "Character Introduction",
        description: "Introduce main characters and establish setting",
        wordCount: "2000-3000 words",
      },
      {
        name: "Rising Action",
        description: "Build tension and develop plot with engaging scenes",
        wordCount: "15000-25000 words",
      },
      {
        name: "Climax",
        description: "Peak dramatic moment and turning point",
        wordCount: "3000-5000 words",
      },
      {
        name: "Falling Action",
        description: "Resolution of conflicts and loose ends",
        wordCount: "2000-4000 words",
      },
      {
        name: "Conclusion/Epilogue",
        description: "Satisfying ending that provides closure",
        wordCount: "1000-2000 words",
      },
    ],
    "non-fiction": [
      {
        name: "Introduction",
        description: "Hook readers and establish value proposition",
        wordCount: "1000-1500 words",
      },
      {
        name: "Foundation Concepts",
        description: "Essential background knowledge and principles",
        wordCount: "3000-5000 words",
      },
      {
        name: "Core Content Chapters",
        description: "Main educational or informational content",
        wordCount: "20000-35000 words",
      },
      {
        name: "Practical Applications",
        description: "Real-world examples and implementation guidance",
        wordCount: "3000-5000 words",
      },
      {
        name: "Advanced Concepts",
        description: "Deeper insights and sophisticated applications",
        wordCount: "3000-5000 words",
      },
      {
        name: "Conclusion & Next Steps",
        description: "Summary and guidance for continued learning",
        wordCount: "1000-2000 words",
      },
    ],
    "self-help": [
      {
        name: "Personal Story/Hook",
        description:
          "Compelling personal narrative that establishes credibility",
        wordCount: "1500-2500 words",
      },
      {
        name: "Problem Identification",
        description: "Clearly define the challenge readers face",
        wordCount: "2000-3000 words",
      },
      {
        name: "Solution Framework",
        description: "Introduce your methodology or approach",
        wordCount: "2000-3000 words",
      },
      {
        name: "Step-by-Step Implementation",
        description: "Detailed guidance with practical exercises",
        wordCount: "15000-25000 words",
      },
      {
        name: "Overcoming Obstacles",
        description: "Address common challenges and setbacks",
        wordCount: "3000-4000 words",
      },
      {
        name: "Transformation Stories",
        description: "Success stories and case studies",
        wordCount: "2000-3000 words",
      },
      {
        name: "Maintaining Progress",
        description: "Long-term strategies for sustained success",
        wordCount: "2000-3000 words",
      },
    ],
    educational: [
      {
        name: "Learning Objectives",
        description: "Clear goals and expectations for readers",
        wordCount: "500-1000 words",
      },
      {
        name: "Foundational Knowledge",
        description: "Essential concepts and terminology",
        wordCount: "3000-5000 words",
      },
      {
        name: "Core Curriculum",
        description: "Main educational content organized by topics",
        wordCount: "20000-40000 words",
      },
      {
        name: "Practical Exercises",
        description: "Hands-on activities and practice problems",
        wordCount: "3000-5000 words",
      },
      {
        name: "Case Studies",
        description: "Real-world applications and examples",
        wordCount: "3000-5000 words",
      },
      {
        name: "Assessment & Review",
        description: "Self-testing and knowledge reinforcement",
        wordCount: "2000-3000 words",
      },
      {
        name: "Further Resources",
        description: "Additional learning materials and references",
        wordCount: "1000-2000 words",
      },
    ],
    biography: [
      {
        name: "Early Life",
        description: "Background, childhood, and formative experiences",
        wordCount: "3000-5000 words",
      },
      {
        name: "Defining Moments",
        description: "Key events that shaped the subject's path",
        wordCount: "4000-6000 words",
      },
      {
        name: "Career/Life Journey",
        description: "Professional development and major achievements",
        wordCount: "10000-20000 words",
      },
      {
        name: "Challenges & Obstacles",
        description: "Difficulties faced and how they were overcome",
        wordCount: "3000-5000 words",
      },
      {
        name: "Relationships & Influences",
        description: "Important people and their impact",
        wordCount: "3000-5000 words",
      },
      {
        name: "Legacy & Impact",
        description: "Lasting contributions and influence",
        wordCount: "2000-4000 words",
      },
    ],
    reference: [
      {
        name: "How to Use This Book",
        description: "Navigation guide and reference instructions",
        wordCount: "500-1000 words",
      },
      {
        name: "Quick Reference Guide",
        description: "Essential information for immediate access",
        wordCount: "2000-3000 words",
      },
      {
        name: "Comprehensive Entries",
        description: "Detailed reference material organized systematically",
        wordCount: "20000-50000 words",
      },
      {
        name: "Cross-References",
        description: "Connections between related topics and concepts",
        wordCount: "1000-2000 words",
      },
      {
        name: "Appendices",
        description: "Additional resources, charts, and supplementary material",
        wordCount: "3000-5000 words",
      },
      {
        name: "Index",
        description: "Searchable index for easy information retrieval",
        wordCount: "1000-2000 words",
      },
    ],
    children: [
      {
        name: "Engaging Opening",
        description: "Fun, colorful introduction that captures young attention",
        wordCount: "200-500 words",
      },
      {
        name: "Story Development",
        description: "Age-appropriate narrative with clear progression",
        wordCount: "1000-3000 words",
      },
      {
        name: "Interactive Elements",
        description: "Questions, activities, or participation opportunities",
        wordCount: "500-1000 words",
      },
      {
        name: "Educational Content",
        description: "Learning objectives woven into entertaining format",
        wordCount: "1000-2000 words",
      },
      {
        name: "Visual Descriptions",
        description: "Rich descriptions that support illustrations",
        wordCount: "500-1000 words",
      },
      {
        name: "Satisfying Conclusion",
        description: "Positive ending with clear resolution",
        wordCount: "200-500 words",
      },
    ],
  };

  return sections[subType] || sections["non-fiction"];
};

/**
 * Get eBook reading context based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Reading context description
 */
const getEbookReadingContext = (subType) => {
  const contexts = {
    fiction: "Entertainment and escapism through compelling storytelling",
    "non-fiction":
      "Education and information acquisition for personal or professional growth",
    "self-help":
      "Personal transformation and improvement through actionable guidance",
    educational:
      "Structured learning and skill development with clear objectives",
    biography: "Inspiration and insight through life stories and experiences",
    reference:
      "Quick information lookup and comprehensive resource consultation",
    children: "Fun, engaging learning experience appropriate for young readers",
  };

  return (
    contexts[subType] ||
    "Engaging digital reading experience with valuable content"
  );
};

/**
 * Get eBook purpose based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Purpose description
 */
const getEbookPurpose = (subType) => {
  const purposes = {
    fiction: "Entertainment, emotional engagement, and storytelling excellence",
    "non-fiction": "Education, information sharing, and knowledge transfer",
    "self-help":
      "Personal development, transformation, and actionable improvement",
    educational:
      "Structured learning, skill building, and academic achievement",
    biography: "Inspiration, historical insight, and personal story sharing",
    reference:
      "Information access, problem-solving, and comprehensive guidance",
    children: "Age-appropriate learning, entertainment, and development",
  };

  return purposes[subType] || "Valuable content delivery and reader engagement";
};

/**
 * Get eBook target length based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Target length description
 */
const getEbookTargetLength = (subType) => {
  const lengths = {
    fiction: "50,000-100,000 words (novel length)",
    "non-fiction": "25,000-60,000 words (comprehensive but accessible)",
    "self-help": "30,000-50,000 words (detailed guidance with examples)",
    educational: "40,000-80,000 words (thorough coverage of subject matter)",
    biography: "60,000-100,000 words (comprehensive life story)",
    reference: "30,000-100,000 words (comprehensive reference material)",
    children: "500-5,000 words (age-appropriate length)",
  };

  return lengths[subType] || "25,000-50,000 words (standard eBook length)";
};

/**
 * Get eBook tone based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Tone description
 */
const getEbookTone = (subType) => {
  const tones = {
    fiction: "Engaging, immersive, emotionally resonant, narrative-driven",
    "non-fiction": "Informative, authoritative, accessible, professional",
    "self-help": "Encouraging, supportive, motivational, practical",
    educational: "Clear, instructional, comprehensive, student-friendly",
    biography: "Respectful, insightful, engaging, historically accurate",
    reference: "Precise, comprehensive, organized, user-friendly",
    children: "Fun, age-appropriate, engaging, positive",
  };

  return tones[subType] || "Engaging, informative, reader-focused";
};

/**
 * Get eBook keywords based on sub-type
 * @param {string} subType - eBook sub-type
 * @returns {string} Relevant keywords
 */
const getEbookKeywords = (subType) => {
  const keywords = {
    fiction: "Story, Novel, Adventure, Romance, Mystery, Thriller",
    "non-fiction": "Learn, Understand, Discover, Explore, Master, Knowledge",
    "self-help": "Transform, Improve, Change, Success, Growth, Breakthrough",
    educational: "Learn, Study, Master, Course, Training, Skills",
    biography: "Life, Story, Journey, Legacy, Inspiration, History",
    reference: "Handbook, Guide, Resource, Encyclopedia, Directory, Compendium",
    children: "Fun, Adventure, Learn, Discover, Friends, Magic",
  };

  return keywords[subType] || "Guide, Learn, Discover, Master, Success";
};

/**
 * Create eBook content prompts for specific sections
 * @param {Object} documentData - Document configuration
 * @param {Object} requirements - eBook requirements
 * @param {string} subType - eBook sub-type
 * @param {string} promptType - Specific prompt type
 * @returns {string} eBook content prompt
 */
const createEbookContentPrompt = (
  documentData,
  requirements,
  subType,
  promptType
) => {
  const baseContext = extractBaseContext(documentData);

  // Add more eBook prompt types as needed
  return `eBook ${promptType} prompt for ${subType} - to be implemented with specialized content for ${baseContext.topic}`;
};

const getCharacterRequirements = (subType) => {
  const requirements = {
    "short-story":
      "Well-developed protagonist with clear motivation and character arc",
    screenplay: "Distinct character voices with visual and behavioral traits",
    memoir: "Authentic personal voice with honest self-reflection",
    poetry: "Persona or voice that carries emotional weight and meaning",
    "creative-nonfiction": "Real people presented with depth and complexity",
  };
  return requirements[subType] || "Compelling characters with authentic voices";
};

const getPlotStructure = (subType) => {
  const structures = {
    "short-story":
      "Classic story arc with setup, conflict, climax, and resolution",
    screenplay:
      "Three-act structure with visual storytelling and scene progression",
    memoir: "Thematic organization around key life events and insights",
    poetry: "Emotional or thematic progression through verses or stanzas",
    "creative-nonfiction": "Narrative structure that serves factual content",
  };
  return (
    structures[subType] ||
    "Clear beginning, middle, and end with satisfying progression"
  );
};

// Report helper functions removed - only supporting ebook, academic, business
