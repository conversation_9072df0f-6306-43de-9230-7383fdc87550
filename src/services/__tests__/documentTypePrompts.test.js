/**
 * Tests for Document Type Prompts
 * Specifically testing business document title generation limits
 */

import { createDocumentTypePrompt } from "../documentTypePrompts.js";

describe("Document Type Prompts", () => {
  describe("Business Document Title Generation", () => {
    test("should request 6 titles for business documents", () => {
      const mockBusinessDocumentData = {
        documentPurpose: {
          primaryType: "business",
          subType: "business-plan",
        },
        topicAndNiche: {
          mainTopic: "Digital Marketing Strategy",
          language: "english",
        },
        audienceAnalysis: {
          primaryAudience: "executives",
        },
      };

      const prompt = createDocumentTypePrompt(
        mockBusinessDocumentData,
        "title"
      );

      // Verify the prompt requests 6 titles specifically
      expect(prompt).toContain(
        "Generate 6 compelling business document titles"
      );
      expect(prompt).not.toContain(
        "Generate 8 compelling business document titles"
      );

      // Verify it's specifically for business documents
      expect(prompt).toContain(
        "business strategist and executive communications specialist"
      );
      expect(prompt).toContain("business document titles");
    });

    test("should include business-specific requirements in title prompt", () => {
      const mockBusinessDocumentData = {
        documentPurpose: {
          primaryType: "business",
          subType: "market-analysis",
        },
        topicAndNiche: {
          mainTopic: "Fintech Market Analysis",
          language: "english",
        },
        audienceAnalysis: {
          primaryAudience: "investors",
        },
      };

      const prompt = createDocumentTypePrompt(
        mockBusinessDocumentData,
        "title"
      );

      // Verify business-specific elements are included
      expect(prompt).toContain("Strategic, Executive, Comprehensive, Analysis");
      expect(prompt).toContain("ROI, Growth, Optimization, Transformation");
      expect(prompt).toContain(
        'strategic", "analytical", "executive", "action-oriented"'
      );
    });
  });

  describe("Other Document Types Title Generation", () => {
    test("should request 8 titles for academic documents", () => {
      const mockAcademicDocumentData = {
        documentPurpose: {
          primaryType: "academic",
          subType: "research-paper",
        },
        topicAndNiche: {
          mainTopic: "Climate Change Research",
          language: "english",
        },
        audienceAnalysis: {
          primaryAudience: "researchers",
        },
      };

      const prompt = createDocumentTypePrompt(
        mockAcademicDocumentData,
        "title"
      );

      // Verify academic documents still request 8 titles
      expect(prompt).toContain("Generate 8 scholarly titles");
      expect(prompt).not.toContain("Generate 6");
    });

    test("should request 8 titles for eBook documents", () => {
      const mockEbookDocumentData = {
        documentPurpose: {
          primaryType: "ebook",
          subType: "self-help",
        },
        topicAndNiche: {
          mainTopic: "Personal Development",
          language: "english",
        },
        audienceAnalysis: {
          primaryAudience: "general public",
        },
      };

      const prompt = createDocumentTypePrompt(mockEbookDocumentData, "title");

      // Verify eBook documents still request 8 titles
      expect(prompt).toContain("Generate 8 compelling eBook titles");
      expect(prompt).not.toContain("Generate 6");
    });
  });

  describe("Document Type Routing", () => {
    test("should route business documents to business prompt", () => {
      const mockBusinessDocumentData = {
        documentPurpose: {
          primaryType: "business",
        },
        topicAndNiche: {
          mainTopic: "Test Topic",
        },
        audienceAnalysis: {
          primaryAudience: "test audience",
        },
      };

      const prompt = createDocumentTypePrompt(
        mockBusinessDocumentData,
        "title"
      );

      // Should contain business-specific content
      expect(prompt).toContain("business strategist");
      expect(prompt).toContain(
        "Generate 6 compelling business document titles"
      );
    });

    test("should route non-business documents to their respective prompts", () => {
      const mockAcademicDocumentData = {
        documentPurpose: {
          primaryType: "academic",
        },
        topicAndNiche: {
          mainTopic: "Test Topic",
        },
        audienceAnalysis: {
          primaryAudience: "test audience",
        },
      };

      const prompt = createDocumentTypePrompt(
        mockAcademicDocumentData,
        "title"
      );

      // Should contain academic-specific content, not business
      expect(prompt).toContain("academic researcher");
      expect(prompt).toContain("Generate 8 scholarly titles");
      expect(prompt).not.toContain("business strategist");
      expect(prompt).not.toContain("Generate 6");
    });
  });
});
